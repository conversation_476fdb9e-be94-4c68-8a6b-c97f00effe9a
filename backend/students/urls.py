from django.urls import path
from . import views

urlpatterns = [
    # Student management endpoints
    path('', views.StudentListCreateView.as_view(), name='student_list_create'),
    path('<int:pk>/', views.StudentDetailView.as_view(), name='student_detail'),
    path('search/', views.StudentSearchView.as_view(), name='student_search'),
    path('stats/', views.StudentStatsView.as_view(), name='student_stats'),
    path('bulk/', views.StudentBulkOperationsView.as_view(), name='student_bulk'),
    
    # Guardian endpoints
    path('<int:student_id>/guardians/', views.GuardianListCreateView.as_view(), name='guardian_list_create'),
    path('<int:student_id>/guardians/<int:pk>/', views.GuardianDetailView.as_view(), name='guardian_detail'),
    
    # Student notes endpoints
    path('<int:student_id>/notes/', views.StudentNoteListCreateView.as_view(), name='student_note_list_create'),
    path('<int:student_id>/notes/<int:pk>/', views.StudentNoteDetailView.as_view(), name='student_note_detail'),
]