from rest_framework import serializers
from .models import Student, Guardian, StudentNote


class GuardianSerializer(serializers.ModelSerializer):
    full_name = serializers.CharField(source='get_full_name', read_only=True)
    
    class Meta:
        model = Guardian
        fields = [
            'id', 'guardian_type', 'first_name', 'last_name', 'full_name',
            'relationship', 'cnic', 'phone_number', 'alternate_phone', 'email',
            'address_line_1', 'address_line_2', 'city', 'province',
            'occupation', 'workplace', 'monthly_income', 'preferred_contact_method',
            'can_pickup_student', 'is_emergency_contact', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class StudentNoteSerializer(serializers.ModelSerializer):
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    
    class Meta:
        model = StudentNote
        fields = [
            'id', 'note_type', 'title', 'content', 'is_confidential',
            'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_by', 'created_at', 'updated_at']


class StudentListSerializer(serializers.ModelSerializer):
    """Simplified serializer for list views"""
    full_name = serializers.CharField(source='get_full_name', read_only=True)
    display_name = serializers.CharField(source='get_display_name', read_only=True)
    age = serializers.IntegerField(read_only=True)
    primary_guardian = serializers.SerializerMethodField()
    
    class Meta:
        model = Student
        fields = [
            'id', 'student_id', 'first_name', 'last_name', 'full_name', 'display_name',
            'date_of_birth', 'age', 'gender', 'phone_number', 'email',
            'city', 'province', 'is_active', 'admission_date', 'primary_guardian'
        ]
    
    def get_primary_guardian(self, obj):
        primary_guardian = obj.guardians.filter(guardian_type='Primary').first()
        if primary_guardian:
            return {
                'name': primary_guardian.get_full_name(),
                'phone': primary_guardian.phone_number,
                'relationship': primary_guardian.relationship
            }
        return None


class StudentDetailSerializer(serializers.ModelSerializer):
    """Detailed serializer for detail views"""
    full_name = serializers.CharField(source='get_full_name', read_only=True)
    display_name = serializers.CharField(source='get_display_name', read_only=True)
    age = serializers.IntegerField(read_only=True)
    guardians = GuardianSerializer(many=True, read_only=True)
    notes = StudentNoteSerializer(many=True, read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    
    class Meta:
        model = Student
        fields = [
            'id', 'student_id', 'first_name', 'last_name', 'father_name', 'full_name', 
            'display_name', 'date_of_birth', 'age', 'gender', 'cnic', 'b_form_number',
            'phone_number', 'email', 'address_line_1', 'address_line_2', 'city', 
            'province', 'postal_code', 'religion', 'nationality', 'marital_status',
            'blood_group', 'photo', 'medical_conditions', 'allergies', 
            'emergency_medical_contact', 'previous_education', 'matric_marks',
            'intermediate_marks', 'is_active', 'admission_date', 'created_at',
            'updated_at', 'created_by', 'created_by_name', 'guardians', 'notes'
        ]
        read_only_fields = ['id', 'student_id', 'age', 'created_at', 'updated_at', 'created_by']


class StudentCreateUpdateSerializer(serializers.ModelSerializer):
    """Serializer for creating and updating students"""
    guardians_data = GuardianSerializer(many=True, required=False, write_only=True)
    
    class Meta:
        model = Student
        fields = [
            'first_name', 'last_name', 'father_name', 'date_of_birth', 'gender',
            'cnic', 'b_form_number', 'phone_number', 'email', 'address_line_1',
            'address_line_2', 'city', 'province', 'postal_code', 'religion',
            'nationality', 'marital_status', 'blood_group', 'photo',
            'medical_conditions', 'allergies', 'emergency_medical_contact',
            'previous_education', 'matric_marks', 'intermediate_marks',
            'is_active', 'guardians_data'
        ]
    
    def create(self, validated_data):
        guardians_data = validated_data.pop('guardians_data', [])
        student = Student.objects.create(**validated_data)
        
        # Create guardians
        for guardian_data in guardians_data:
            Guardian.objects.create(student=student, **guardian_data)
        
        return student
    
    def update(self, instance, validated_data):
        guardians_data = validated_data.pop('guardians_data', None)
        
        # Update student fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # Update guardians if provided
        if guardians_data is not None:
            # Delete existing guardians and create new ones
            instance.guardians.all().delete()
            for guardian_data in guardians_data:
                Guardian.objects.create(student=instance, **guardian_data)
        
        return instance
    
    def validate_cnic(self, value):
        if value:
            # Check for uniqueness
            if Student.objects.filter(cnic=value).exclude(pk=self.instance.pk if self.instance else None).exists():
                raise serializers.ValidationError("A student with this CNIC already exists.")
        return value
    
    def validate_phone_number(self, value):
        if value:
            # Check for uniqueness
            if Student.objects.filter(phone_number=value).exclude(pk=self.instance.pk if self.instance else None).exists():
                raise serializers.ValidationError("A student with this phone number already exists.")
        return value
    
    def validate(self, data):
        # Ensure either CNIC or B-Form is provided
        if not data.get('cnic') and not data.get('b_form_number'):
            raise serializers.ValidationError("Either CNIC or B-Form number must be provided.")
        
        return data