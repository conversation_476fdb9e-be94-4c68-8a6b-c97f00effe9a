from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django.db.models import Q
from django.shortcuts import get_object_or_404
from common.mixins import StandardResponseMixin
from common.pagination import StandardPageNumberPagination
from .models import Student, Guardian, StudentNote
from .serializers import (
    StudentListSerializer, StudentDetailSerializer, 
    StudentCreateUpdateSerializer, GuardianSerializer, StudentNoteSerializer
)


class StudentListCreateView(StandardResponseMixin, generics.ListCreateAPIView):
    """
    List all students or create a new student
    """
    queryset = Student.objects.all()
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = StandardPageNumberPagination
    
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return StudentCreateUpdateSerializer
        return StudentListSerializer
    
    def get_queryset(self):
        queryset = Student.objects.select_related('created_by').prefetch_related('guardians')
        
        # Search functionality
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(first_name__icontains=search) |
                Q(last_name__icontains=search) |
                Q(student_id__icontains=search) |
                Q(father_name__icontains=search) |
                Q(phone_number__icontains=search) |
                Q(cnic__icontains=search)
            )
        
        # Filter by active status
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')
        
        # Filter by province
        province = self.request.query_params.get('province')
        if province:
            queryset = queryset.filter(province=province)
        
        # Filter by city
        city = self.request.query_params.get('city')
        if city:
            queryset = queryset.filter(city=city)
        
        # Filter by gender
        gender = self.request.query_params.get('gender')
        if gender:
            queryset = queryset.filter(gender=gender.upper())
        
        return queryset.order_by('-admission_date', 'student_id')
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)


class StudentDetailView(StandardResponseMixin, generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update or delete a student
    """
    queryset = Student.objects.all()
    permission_classes = [permissions.IsAuthenticated]
    
    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return StudentCreateUpdateSerializer
        return StudentDetailSerializer
    
    def get_queryset(self):
        return Student.objects.select_related('created_by').prefetch_related(
            'guardians', 'notes', 'notes__created_by'
        )


class StudentSearchView(StandardResponseMixin, APIView):
    """
    Advanced search for students
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        query = request.query_params.get('q', '')
        if not query:
            return self.error_response("Search query is required", status.HTTP_400_BAD_REQUEST)
        
        students = Student.objects.filter(
            Q(first_name__icontains=query) |
            Q(last_name__icontains=query) |
            Q(student_id__icontains=query) |
            Q(father_name__icontains=query) |
            Q(phone_number__icontains=query) |
            Q(cnic__icontains=query) |
            Q(guardians__first_name__icontains=query) |
            Q(guardians__last_name__icontains=query) |
            Q(guardians__phone_number__icontains=query)
        ).distinct()[:20]  # Limit to 20 results
        
        serializer = StudentListSerializer(students, many=True)
        return self.success_response(
            data=serializer.data,
            message=f"Found {len(serializer.data)} students"
        )


class StudentStatsView(StandardResponseMixin, APIView):
    """
    Get student statistics
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        from django.utils import timezone
        from datetime import timedelta
        
        stats = {}
        
        # Basic counts
        stats['total_students'] = Student.objects.count()
        stats['active_students'] = Student.objects.filter(is_active=True).count()
        stats['inactive_students'] = Student.objects.filter(is_active=False).count()
        
        # New students this month
        this_month = timezone.now().replace(day=1)
        stats['new_this_month'] = Student.objects.filter(admission_date__gte=this_month).count()
        
        # Gender distribution
        stats['gender_distribution'] = {
            'male': Student.objects.filter(gender='M').count(),
            'female': Student.objects.filter(gender='F').count(),
            'other': Student.objects.filter(gender='O').count(),
        }
        
        # Province distribution
        from django.db import models
        province_stats = Student.objects.values('province').annotate(
            count=models.Count('id')
        ).order_by('-count')
        stats['province_distribution'] = list(province_stats)
        
        return self.success_response(
            data=stats,
            message="Student statistics retrieved successfully"
        )


# Guardian related views
class GuardianListCreateView(StandardResponseMixin, generics.ListCreateAPIView):
    """
    List guardians for a student or create a new guardian
    """
    serializer_class = GuardianSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        student_id = self.kwargs['student_id']
        return Guardian.objects.filter(student_id=student_id)
    
    def perform_create(self, serializer):
        student_id = self.kwargs['student_id']
        student = get_object_or_404(Student, id=student_id)
        serializer.save(student=student)


class GuardianDetailView(StandardResponseMixin, generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update or delete a guardian
    """
    serializer_class = GuardianSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        student_id = self.kwargs['student_id']
        return Guardian.objects.filter(student_id=student_id)


# Student Notes related views
class StudentNoteListCreateView(StandardResponseMixin, generics.ListCreateAPIView):
    """
    List notes for a student or create a new note
    """
    serializer_class = StudentNoteSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        student_id = self.kwargs['student_id']
        return StudentNote.objects.filter(student_id=student_id).select_related('created_by')
    
    def perform_create(self, serializer):
        student_id = self.kwargs['student_id']
        student = get_object_or_404(Student, id=student_id)
        serializer.save(student=student, created_by=self.request.user)


class StudentNoteDetailView(StandardResponseMixin, generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update or delete a student note
    """
    serializer_class = StudentNoteSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        student_id = self.kwargs['student_id']
        return StudentNote.objects.filter(student_id=student_id)


# Bulk operations
class StudentBulkOperationsView(StandardResponseMixin, APIView):
    """
    Bulk operations for students
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        operation = request.data.get('operation')
        student_ids = request.data.get('student_ids', [])
        
        if not operation or not student_ids:
            return self.error_response(
                "Operation and student_ids are required",
                status.HTTP_400_BAD_REQUEST
            )
        
        students = Student.objects.filter(id__in=student_ids)
        
        if operation == 'activate':
            students.update(is_active=True)
            message = f"Activated {students.count()} students"
        elif operation == 'deactivate':
            students.update(is_active=False)
            message = f"Deactivated {students.count()} students"
        elif operation == 'delete':
            count = students.count()
            students.delete()
            message = f"Deleted {count} students"
        else:
            return self.error_response(
                "Invalid operation. Supported: activate, deactivate, delete",
                status.HTTP_400_BAD_REQUEST
            )
        
        return self.success_response(
            data={'affected_count': students.count()},
            message=message
        )
