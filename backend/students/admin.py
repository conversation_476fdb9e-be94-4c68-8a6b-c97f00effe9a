from django.contrib import admin
from simple_history.admin import SimpleHistoryAdmin
from .models import Student, Guardian, StudentNote


@admin.register(Student)
class StudentAdmin(SimpleHistoryAdmin):
    list_display = [
        'student_id', 'first_name', 'last_name', 'father_name', 
        'cnic', 'phone_number', 'city', 'province', 'is_active', 'admission_date'
    ]
    list_filter = [
        'is_active', 'gender', 'province', 'religion', 'marital_status', 
        'admission_date', 'created_at'
    ]
    search_fields = [
        'student_id', 'first_name', 'last_name', 'father_name', 
        'cnic', 'phone_number', 'email'
    ]
    readonly_fields = ['student_id', 'admission_date', 'created_at', 'updated_at']
    fieldsets = [
        ('Basic Information', {
            'fields': [
                'student_id', 'first_name', 'last_name', 'father_name',
                'date_of_birth', 'gender', 'photo'
            ]
        }),
        ('Identity Documents', {
            'fields': ['cnic', 'b_form_number']
        }),
        ('Contact Information', {
            'fields': ['phone_number', 'email']
        }),
        ('Address', {
            'fields': [
                'address_line_1', 'address_line_2', 'city', 
                'province', 'postal_code'
            ]
        }),
        ('Personal Details', {
            'fields': [
                'religion', 'nationality', 'marital_status', 'blood_group'
            ]
        }),
        ('Medical Information', {
            'fields': [
                'medical_conditions', 'allergies', 'emergency_medical_contact'
            ],
            'classes': ['collapse']
        }),
        ('Academic Background', {
            'fields': [
                'previous_education', 'matric_marks', 'intermediate_marks'
            ],
            'classes': ['collapse']
        }),
        ('System Information', {
            'fields': [
                'is_active', 'admission_date', 'created_by', 
                'created_at', 'updated_at'
            ],
            'classes': ['collapse']
        })
    ]
    
    def save_model(self, request, obj, form, change):
        if not change:  # Creating new student
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(Guardian)
class GuardianAdmin(SimpleHistoryAdmin):
    list_display = [
        'get_full_name', 'relationship', 'guardian_type', 
        'student', 'cnic', 'phone_number', 'is_active'
    ]
    list_filter = [
        'guardian_type', 'relationship', 'is_active', 
        'can_pickup_student', 'is_emergency_contact', 'province'
    ]
    search_fields = [
        'first_name', 'last_name', 'cnic', 'phone_number', 
        'student__first_name', 'student__last_name', 'student__student_id'
    ]
    readonly_fields = ['created_at', 'updated_at']
    fieldsets = [
        ('Student Information', {
            'fields': ['student']
        }),
        ('Guardian Information', {
            'fields': [
                'guardian_type', 'first_name', 'last_name', 
                'relationship', 'cnic'
            ]
        }),
        ('Contact Information', {
            'fields': [
                'phone_number', 'alternate_phone', 'email',
                'preferred_contact_method'
            ]
        }),
        ('Address', {
            'fields': [
                'address_line_1', 'address_line_2', 'city', 'province'
            ]
        }),
        ('Professional Information', {
            'fields': ['occupation', 'workplace', 'monthly_income'],
            'classes': ['collapse']
        }),
        ('Permissions', {
            'fields': [
                'can_pickup_student', 'is_emergency_contact', 'is_active'
            ]
        }),
        ('System Information', {
            'fields': ['created_at', 'updated_at'],
            'classes': ['collapse']
        })
    ]
    
    def get_full_name(self, obj):
        return obj.get_full_name()
    get_full_name.short_description = 'Full Name'


@admin.register(StudentNote)
class StudentNoteAdmin(SimpleHistoryAdmin):
    list_display = [
        'title', 'student', 'note_type', 'is_confidential', 
        'created_by', 'created_at'
    ]
    list_filter = [
        'note_type', 'is_confidential', 'created_at'
    ]
    search_fields = [
        'title', 'content', 'student__first_name', 
        'student__last_name', 'student__student_id'
    ]
    readonly_fields = ['created_at', 'updated_at']
    fieldsets = [
        ('Note Information', {
            'fields': ['student', 'note_type', 'title', 'content']
        }),
        ('Privacy & System', {
            'fields': [
                'is_confidential', 'created_by', 'created_at', 'updated_at'
            ]
        })
    ]
    
    def save_model(self, request, obj, form, change):
        if not change:  # Creating new note
            obj.created_by = request.user
        super().save_model(request, obj, form, change)
