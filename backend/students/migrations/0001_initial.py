# Generated by Django 5.2.4 on 2025-07-25 20:20

import django.core.validators
import django.db.models.deletion
import simple_history.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='HistoricalStudent',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('student_id', models.Char<PERSON><PERSON>(db_index=True, max_length=20)),
                ('first_name', models.Char<PERSON>ield(max_length=50)),
                ('last_name', models.<PERSON>r<PERSON><PERSON>(max_length=50)),
                ('father_name', models.Char<PERSON>ield(max_length=100)),
                ('date_of_birth', models.DateField()),
                ('gender', models.<PERSON>r<PERSON><PERSON>(choices=[('M', 'Male'), ('F', 'Female'), ('O', 'Other')], max_length=1)),
                ('cnic', models.CharField(blank=True, help_text='Format: 12345-1234567-1', max_length=15, null=True, validators=[django.core.validators.RegexValidator(message='CNIC must be in format: 12345-1234567-1', regex='^\\d{5}-\\d{7}-\\d{1}$')])),
                ('b_form_number', models.CharField(blank=True, help_text='B-Form number for minors', max_length=20, null=True)),
                ('phone_number', models.CharField(help_text='Format: +923001234567 or 03001234567', max_length=15, validators=[django.core.validators.RegexValidator(message='Phone must be in format: +923001234567 or 03001234567', regex='^\\+92\\d{10}$|^0\\d{10}$')])),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('address_line_1', models.CharField(max_length=200)),
                ('address_line_2', models.CharField(blank=True, max_length=200, null=True)),
                ('city', models.CharField(max_length=50)),
                ('province', models.CharField(choices=[('Punjab', 'Punjab'), ('Sindh', 'Sindh'), ('KPK', 'Khyber Pakhtunkhwa'), ('Balochistan', 'Balochistan'), ('Gilgit-Baltistan', 'Gilgit-Baltistan'), ('AJK', 'Azad Jammu & Kashmir'), ('Islamabad', 'Islamabad Capital Territory')], max_length=50)),
                ('postal_code', models.CharField(blank=True, max_length=10, null=True)),
                ('religion', models.CharField(choices=[('Islam', 'Islam'), ('Christianity', 'Christianity'), ('Hinduism', 'Hinduism'), ('Buddhism', 'Buddhism'), ('Other', 'Other')], default='Islam', max_length=20)),
                ('nationality', models.CharField(default='Pakistani', max_length=50)),
                ('marital_status', models.CharField(choices=[('S', 'Single'), ('M', 'Married'), ('D', 'Divorced'), ('W', 'Widowed')], default='S', max_length=1)),
                ('blood_group', models.CharField(blank=True, choices=[('A+', 'A+'), ('A-', 'A-'), ('B+', 'B+'), ('B-', 'B-'), ('AB+', 'AB+'), ('AB-', 'AB-'), ('O+', 'O+'), ('O-', 'O-')], max_length=3, null=True)),
                ('photo', models.TextField(blank=True, max_length=100, null=True)),
                ('medical_conditions', models.TextField(blank=True, null=True)),
                ('allergies', models.TextField(blank=True, null=True)),
                ('emergency_medical_contact', models.CharField(blank=True, max_length=15, null=True)),
                ('previous_education', models.TextField(blank=True, null=True)),
                ('matric_marks', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('intermediate_marks', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('admission_date', models.DateField(blank=True, editable=False)),
                ('created_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical student',
                'verbose_name_plural': 'historical students',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='Student',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('student_id', models.CharField(db_index=True, max_length=20, unique=True)),
                ('first_name', models.CharField(max_length=50)),
                ('last_name', models.CharField(max_length=50)),
                ('father_name', models.CharField(max_length=100)),
                ('date_of_birth', models.DateField()),
                ('gender', models.CharField(choices=[('M', 'Male'), ('F', 'Female'), ('O', 'Other')], max_length=1)),
                ('cnic', models.CharField(blank=True, help_text='Format: 12345-1234567-1', max_length=15, null=True, validators=[django.core.validators.RegexValidator(message='CNIC must be in format: 12345-1234567-1', regex='^\\d{5}-\\d{7}-\\d{1}$')])),
                ('b_form_number', models.CharField(blank=True, help_text='B-Form number for minors', max_length=20, null=True)),
                ('phone_number', models.CharField(help_text='Format: +923001234567 or 03001234567', max_length=15, validators=[django.core.validators.RegexValidator(message='Phone must be in format: +923001234567 or 03001234567', regex='^\\+92\\d{10}$|^0\\d{10}$')])),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('address_line_1', models.CharField(max_length=200)),
                ('address_line_2', models.CharField(blank=True, max_length=200, null=True)),
                ('city', models.CharField(max_length=50)),
                ('province', models.CharField(choices=[('Punjab', 'Punjab'), ('Sindh', 'Sindh'), ('KPK', 'Khyber Pakhtunkhwa'), ('Balochistan', 'Balochistan'), ('Gilgit-Baltistan', 'Gilgit-Baltistan'), ('AJK', 'Azad Jammu & Kashmir'), ('Islamabad', 'Islamabad Capital Territory')], max_length=50)),
                ('postal_code', models.CharField(blank=True, max_length=10, null=True)),
                ('religion', models.CharField(choices=[('Islam', 'Islam'), ('Christianity', 'Christianity'), ('Hinduism', 'Hinduism'), ('Buddhism', 'Buddhism'), ('Other', 'Other')], default='Islam', max_length=20)),
                ('nationality', models.CharField(default='Pakistani', max_length=50)),
                ('marital_status', models.CharField(choices=[('S', 'Single'), ('M', 'Married'), ('D', 'Divorced'), ('W', 'Widowed')], default='S', max_length=1)),
                ('blood_group', models.CharField(blank=True, choices=[('A+', 'A+'), ('A-', 'A-'), ('B+', 'B+'), ('B-', 'B-'), ('AB+', 'AB+'), ('AB-', 'AB-'), ('O+', 'O+'), ('O-', 'O-')], max_length=3, null=True)),
                ('photo', models.ImageField(blank=True, null=True, upload_to='student_photos/')),
                ('medical_conditions', models.TextField(blank=True, null=True)),
                ('allergies', models.TextField(blank=True, null=True)),
                ('emergency_medical_contact', models.CharField(blank=True, max_length=15, null=True)),
                ('previous_education', models.TextField(blank=True, null=True)),
                ('matric_marks', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('intermediate_marks', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('admission_date', models.DateField(auto_now_add=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_students', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-admission_date', 'student_id'],
            },
        ),
        migrations.CreateModel(
            name='HistoricalStudentNote',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('note_type', models.CharField(choices=[('Academic', 'Academic'), ('Behavioral', 'Behavioral'), ('Medical', 'Medical'), ('Administrative', 'Administrative'), ('Other', 'Other')], max_length=20)),
                ('title', models.CharField(max_length=200)),
                ('content', models.TextField()),
                ('is_confidential', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('student', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='students.student')),
            ],
            options={
                'verbose_name': 'historical student note',
                'verbose_name_plural': 'historical student notes',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalGuardian',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('guardian_type', models.CharField(choices=[('Primary', 'Primary Guardian'), ('Secondary', 'Secondary Guardian'), ('Emergency', 'Emergency Contact'), ('Pickup', 'Authorized Pickup Person')], max_length=10)),
                ('first_name', models.CharField(max_length=50)),
                ('last_name', models.CharField(max_length=50)),
                ('relationship', models.CharField(choices=[('Father', 'Father'), ('Mother', 'Mother'), ('Brother', 'Brother'), ('Sister', 'Sister'), ('Uncle', 'Uncle'), ('Aunt', 'Aunt'), ('Grandfather', 'Grandfather'), ('Grandmother', 'Grandmother'), ('Guardian', 'Legal Guardian'), ('Other', 'Other')], max_length=20)),
                ('cnic', models.CharField(help_text='Format: 12345-1234567-1', max_length=15, validators=[django.core.validators.RegexValidator(message='CNIC must be in format: 12345-1234567-1', regex='^\\d{5}-\\d{7}-\\d{1}$')])),
                ('phone_number', models.CharField(max_length=15, validators=[django.core.validators.RegexValidator(message='Phone must be in format: +923001234567 or 03001234567', regex='^\\+92\\d{10}$|^0\\d{10}$')])),
                ('alternate_phone', models.CharField(blank=True, max_length=15, null=True, validators=[django.core.validators.RegexValidator(message='Phone must be in format: +923001234567 or 03001234567', regex='^\\+92\\d{10}$|^0\\d{10}$')])),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('address_line_1', models.CharField(max_length=200)),
                ('address_line_2', models.CharField(blank=True, max_length=200, null=True)),
                ('city', models.CharField(max_length=50)),
                ('province', models.CharField(choices=[('Punjab', 'Punjab'), ('Sindh', 'Sindh'), ('KPK', 'Khyber Pakhtunkhwa'), ('Balochistan', 'Balochistan'), ('Gilgit-Baltistan', 'Gilgit-Baltistan'), ('AJK', 'Azad Jammu & Kashmir'), ('Islamabad', 'Islamabad Capital Territory')], max_length=50)),
                ('occupation', models.CharField(blank=True, max_length=100, null=True)),
                ('workplace', models.CharField(blank=True, max_length=200, null=True)),
                ('monthly_income', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('preferred_contact_method', models.CharField(choices=[('Phone', 'Phone Call'), ('SMS', 'SMS'), ('Email', 'Email'), ('WhatsApp', 'WhatsApp')], default='Phone', max_length=10)),
                ('can_pickup_student', models.BooleanField(default=False)),
                ('is_emergency_contact', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('student', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='students.student')),
            ],
            options={
                'verbose_name': 'historical guardian',
                'verbose_name_plural': 'historical guardians',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='Guardian',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('guardian_type', models.CharField(choices=[('Primary', 'Primary Guardian'), ('Secondary', 'Secondary Guardian'), ('Emergency', 'Emergency Contact'), ('Pickup', 'Authorized Pickup Person')], max_length=10)),
                ('first_name', models.CharField(max_length=50)),
                ('last_name', models.CharField(max_length=50)),
                ('relationship', models.CharField(choices=[('Father', 'Father'), ('Mother', 'Mother'), ('Brother', 'Brother'), ('Sister', 'Sister'), ('Uncle', 'Uncle'), ('Aunt', 'Aunt'), ('Grandfather', 'Grandfather'), ('Grandmother', 'Grandmother'), ('Guardian', 'Legal Guardian'), ('Other', 'Other')], max_length=20)),
                ('cnic', models.CharField(help_text='Format: 12345-1234567-1', max_length=15, validators=[django.core.validators.RegexValidator(message='CNIC must be in format: 12345-1234567-1', regex='^\\d{5}-\\d{7}-\\d{1}$')])),
                ('phone_number', models.CharField(max_length=15, validators=[django.core.validators.RegexValidator(message='Phone must be in format: +923001234567 or 03001234567', regex='^\\+92\\d{10}$|^0\\d{10}$')])),
                ('alternate_phone', models.CharField(blank=True, max_length=15, null=True, validators=[django.core.validators.RegexValidator(message='Phone must be in format: +923001234567 or 03001234567', regex='^\\+92\\d{10}$|^0\\d{10}$')])),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('address_line_1', models.CharField(max_length=200)),
                ('address_line_2', models.CharField(blank=True, max_length=200, null=True)),
                ('city', models.CharField(max_length=50)),
                ('province', models.CharField(choices=[('Punjab', 'Punjab'), ('Sindh', 'Sindh'), ('KPK', 'Khyber Pakhtunkhwa'), ('Balochistan', 'Balochistan'), ('Gilgit-Baltistan', 'Gilgit-Baltistan'), ('AJK', 'Azad Jammu & Kashmir'), ('Islamabad', 'Islamabad Capital Territory')], max_length=50)),
                ('occupation', models.CharField(blank=True, max_length=100, null=True)),
                ('workplace', models.CharField(blank=True, max_length=200, null=True)),
                ('monthly_income', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('preferred_contact_method', models.CharField(choices=[('Phone', 'Phone Call'), ('SMS', 'SMS'), ('Email', 'Email'), ('WhatsApp', 'WhatsApp')], default='Phone', max_length=10)),
                ('can_pickup_student', models.BooleanField(default=False)),
                ('is_emergency_contact', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='guardians', to='students.student')),
            ],
            options={
                'ordering': ['guardian_type', 'first_name'],
            },
        ),
        migrations.CreateModel(
            name='StudentNote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('note_type', models.CharField(choices=[('Academic', 'Academic'), ('Behavioral', 'Behavioral'), ('Medical', 'Medical'), ('Administrative', 'Administrative'), ('Other', 'Other')], max_length=20)),
                ('title', models.CharField(max_length=200)),
                ('content', models.TextField()),
                ('is_confidential', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notes', to='students.student')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='student',
            index=models.Index(fields=['student_id'], name='students_st_student_e7126a_idx'),
        ),
        migrations.AddIndex(
            model_name='student',
            index=models.Index(fields=['cnic'], name='students_st_cnic_014a50_idx'),
        ),
        migrations.AddIndex(
            model_name='student',
            index=models.Index(fields=['phone_number'], name='students_st_phone_n_97f681_idx'),
        ),
        migrations.AddIndex(
            model_name='student',
            index=models.Index(fields=['admission_date'], name='students_st_admissi_4d51f0_idx'),
        ),
        migrations.AddIndex(
            model_name='guardian',
            index=models.Index(fields=['student', 'guardian_type'], name='students_gu_student_c7d3bd_idx'),
        ),
        migrations.AddIndex(
            model_name='guardian',
            index=models.Index(fields=['cnic'], name='students_gu_cnic_f062a8_idx'),
        ),
        migrations.AddIndex(
            model_name='guardian',
            index=models.Index(fields=['phone_number'], name='students_gu_phone_n_4ebc24_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='guardian',
            unique_together={('student', 'cnic')},
        ),
    ]
