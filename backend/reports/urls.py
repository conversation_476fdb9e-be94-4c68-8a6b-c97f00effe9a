from django.urls import path
from . import views

app_name = 'reports'

urlpatterns = [
    # Import URLs
    path('import/students/', views.StudentImportView.as_view(), name='import_students'),
    path('import/payments/', views.PaymentImportView.as_view(), name='import_payments'),
    
    # Export URLs
    path('export/ledgers/', views.export_student_ledgers, name='export_ledgers'),
    path('export/rosters/', views.export_class_rosters, name='export_rosters'),
    path('export/financial-summary/', views.export_financial_summary, name='export_financial_summary'),
    
    # Reporting URLs
    path('daily-collection/', views.daily_collection_report, name='daily_collection_report'),
    
    # Bank Reconciliation
    path('bank-reconciliation/', views.BankReconciliationView.as_view(), name='bank_reconciliation'),
]