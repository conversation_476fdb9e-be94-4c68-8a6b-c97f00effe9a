<!DOCTYPE html>
<html>
<head>
    <title>Bank Reconciliation - GIHD SMS</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="file"] { width: 100%; padding: 8px; border: 1px solid #ddd; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #005a87; }
        .alert { padding: 15px; margin: 15px 0; border-radius: 4px; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .alert-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .requirements { background: #f8f9fa; padding: 15px; border-left: 4px solid #007cba; margin-bottom: 20px; }
        .results-section { margin-top: 30px; }
        .tabs { border-bottom: 1px solid #ddd; margin-bottom: 20px; }
        .tab { display: inline-block; padding: 10px 20px; background: #f8f9fa; border: 1px solid #ddd; margin-right: 5px; cursor: pointer; }
        .tab.active { background: #007cba; color: white; border-bottom: 1px solid #007cba; }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f8f9fa; font-weight: bold; }
        .matched-row { background: #d4edda; }
        .unmatched-row { background: #fff3cd; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Bank Reconciliation</h1>
        
        <div class="requirements">
            <h3>Bank Statement Requirements:</h3>
            <ul>
                <li>Supported formats: CSV, Excel (.xlsx, .xls)</li>
                <li>Maximum file size: 10MB</li>
                <li>Required columns: Date, Description (amount columns will be auto-detected)</li>
                <li>Common column names: date/transaction_date, description/narration, debit/credit, balance</li>
                <li>Date format: Any standard format (YYYY-MM-DD, DD/MM/YYYY, etc.)</li>
            </ul>
        </div>
        
        <form id="reconciliationForm" enctype="multipart/form-data">
            <div class="form-group">
                <label for="bank_statement">Select Bank Statement File:</label>
                <input type="file" id="bank_statement" name="bank_statement" accept=".csv,.xlsx,.xls" required>
            </div>
            
            <button type="submit">Process Bank Statement</button>
        </form>
        
        <div id="results" class="results-section"></div>
    </div>

    <script>
        document.getElementById('reconciliationForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData();
            const fileInput = document.getElementById('bank_statement');
            formData.append('bank_statement', fileInput.files[0]);
            
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<p>Processing bank statement... Please wait.</p>';
            
            fetch('', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': getCookie('csrftoken')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayResults(data);
                } else {
                    resultsDiv.innerHTML = `<div class="alert alert-error">
                        <h4>Processing Failed!</h4>
                        <p>${data.error}</p>
                    </div>`;
                }
            })
            .catch(error => {
                resultsDiv.innerHTML = `<div class="alert alert-error">
                    <h4>Error!</h4>
                    <p>An unexpected error occurred: ${error.message}</p>
                </div>`;
            });
        });
        
        function displayResults(data) {
            const resultsDiv = document.getElementById('results');
            
            let html = `
                <div class="alert alert-info">
                    <h4>Reconciliation Summary</h4>
                    <p><strong>Matched Payments:</strong> ${data.matched_count}</p>
                    <p><strong>Unmatched Bank Entries:</strong> ${data.unmatched_count}</p>
                </div>
                
                <div class="tabs">
                    <div class="tab active" onclick="showTab('matched')">Matched Payments (${data.matched_count})</div>
                    <div class="tab" onclick="showTab('unmatched')">Unmatched Entries (${data.unmatched_count})</div>
                </div>
                
                <div id="matched-tab" class="tab-content active">
                    <h3>Matched Payments</h3>
                    <table>
                        <thead>
                            <tr>
                                <th>Bank Date</th>
                                <th>Bank Amount</th>
                                <th>Bank Description</th>
                                <th>Payment ID</th>
                                <th>Student Name</th>
                                <th>Payment Amount</th>
                                <th>Payment Date</th>
                            </tr>
                        </thead>
                        <tbody>`;
            
            data.matched_payments.forEach(match => {
                html += `<tr class="matched-row">
                    <td>${match.bank_date}</td>
                    <td>PKR ${match.bank_amount.toLocaleString()}</td>
                    <td>${match.bank_description}</td>
                    <td>${match.payment_id}</td>
                    <td>${match.student_name}</td>
                    <td>PKR ${match.payment_amount.toLocaleString()}</td>
                    <td>${match.payment_date}</td>
                </tr>`;
            });
            
            html += `</tbody></table></div>
                
                <div id="unmatched-tab" class="tab-content">
                    <h3>Unmatched Bank Entries</h3>
                    <table>
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Amount</th>
                                <th>Description</th>
                                <th>Reference</th>
                            </tr>
                        </thead>
                        <tbody>`;
            
            data.unmatched_entries.forEach(entry => {
                html += `<tr class="unmatched-row">
                    <td>${entry.date}</td>
                    <td>PKR ${entry.amount.toLocaleString()}</td>
                    <td>${entry.description}</td>
                    <td>${entry.reference}</td>
                </tr>`;
            });
            
            html += `</tbody></table></div>`;
            
            resultsDiv.innerHTML = html;
        }
        
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(tabName + '-tab').classList.add('active');
            event.target.classList.add('active');
        }
        
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
    </script>
</body>
</html>