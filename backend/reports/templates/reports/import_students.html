<!DOCTYPE html>
<html>
<head>
    <title>Import Students - GIHD SMS</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="file"] { width: 100%; padding: 8px; border: 1px solid #ddd; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #005a87; }
        .alert { padding: 15px; margin: 15px 0; border-radius: 4px; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .requirements { background: #f8f9fa; padding: 15px; border-left: 4px solid #007cba; margin-bottom: 20px; }
        .sample-format { background: #fff3cd; padding: 10px; border-radius: 4px; margin-top: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Import Students</h1>
        
        <div class="requirements">
            <h3>File Requirements:</h3>
            <ul>
                <li>Supported formats: CSV, Excel (.xlsx, .xls)</li>
                <li>Maximum file size: 10MB</li>
                <li>Required columns: first_name, last_name, father_name, date_of_birth, gender, phone_number, address_line_1, city, province</li>
                <li>Optional columns: cnic, email, religion, nationality</li>
            </ul>
            
            <div class="sample-format">
                <strong>Sample CSV format:</strong><br>
                <code>
                first_name,last_name,father_name,date_of_birth,gender,cnic,phone_number,email,address_line_1,city,province<br>
                Ahmed,Ali,Muhammad Ali,1995-05-15,M,12345-1234567-1,03001234567,<EMAIL>,House 123 Street 1,Lahore,Punjab
                </code>
            </div>
        </div>
        
        <form id="importForm" enctype="multipart/form-data">
            <div class="form-group">
                <label for="file">Select CSV/Excel File:</label>
                <input type="file" id="file" name="file" accept=".csv,.xlsx,.xls" required>
            </div>
            
            <button type="submit">Import Students</button>
        </form>
        
        <div id="results"></div>
    </div>

    <script>
        document.getElementById('importForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData();
            const fileInput = document.getElementById('file');
            formData.append('file', fileInput.files[0]);
            
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<p>Importing... Please wait.</p>';
            
            fetch('', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': getCookie('csrftoken')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    let html = `<div class="alert alert-success">
                        <h4>Import Successful!</h4>
                        <p>Imported ${data.imported_count} students successfully.</p>
                    </div>`;
                    
                    if (data.errors && data.errors.length > 0) {
                        html += `<div class="alert alert-error">
                            <h4>Some errors occurred:</h4>
                            <ul>`;
                        data.errors.forEach(error => {
                            html += `<li>${error}</li>`;
                        });
                        html += `</ul></div>`;
                    }
                    
                    resultsDiv.innerHTML = html;
                } else {
                    let html = `<div class="alert alert-error">
                        <h4>Import Failed!</h4>`;
                    
                    if (data.errors) {
                        html += '<ul>';
                        data.errors.forEach(error => {
                            html += `<li>${error}</li>`;
                        });
                        html += '</ul>';
                    } else if (data.error) {
                        html += `<p>${data.error}</p>`;
                    }
                    
                    html += '</div>';
                    resultsDiv.innerHTML = html;
                }
            })
            .catch(error => {
                resultsDiv.innerHTML = `<div class="alert alert-error">
                    <h4>Error!</h4>
                    <p>An unexpected error occurred: ${error.message}</p>
                </div>`;
            });
        });
        
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
    </script>
</body>
</html>