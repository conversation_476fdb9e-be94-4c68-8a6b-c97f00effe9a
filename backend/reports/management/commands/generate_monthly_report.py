from django.core.management.base import BaseCommand, CommandError
from django.core.mail import EmailMessage
from django.conf import settings
from datetime import datetime, date, timedelta
from decimal import Decimal
import pandas as pd

from fees.models import StudentPayment, StudentEnrollment
from academics.models import AcademicYear
from reports.utils import ExportService, ReportGenerator


class Command(BaseCommand):
    help = 'Generate monthly financial reports and optionally email them'

    def add_arguments(self, parser):
        parser.add_argument(
            '--month',
            type=int,
            help='Month (1-12) to generate report for. Defaults to last month.'
        )
        parser.add_argument(
            '--year',
            type=int,
            help='Year to generate report for. Defaults to current year.'
        )
        parser.add_argument(
            '--email',
            nargs='+',
            help='Email addresses to send the report to'
        )
        parser.add_argument(
            '--save-path',
            type=str,
            help='Path to save the report file'
        )

    def handle(self, *args, **options):
        # Determine report period
        if options['month'] and options['year']:
            report_month = options['month']
            report_year = options['year']
        else:
            # Default to last month
            last_month = date.today().replace(day=1) - timedelta(days=1)
            report_month = last_month.month
            report_year = last_month.year

        # Validate month
        if not (1 <= report_month <= 12):
            raise CommandError('Month must be between 1 and 12')

        # Calculate date range
        start_date = date(report_year, report_month, 1)
        if report_month == 12:
            end_date = date(report_year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = date(report_year, report_month + 1, 1) - timedelta(days=1)

        self.stdout.write(
            self.style.SUCCESS(
                f'Generating monthly report for {start_date.strftime("%B %Y")}...'
            )
        )

        try:
            # Generate report data
            report_data = self.generate_report_data(start_date, end_date)
            
            # Create Excel file
            filename = f"monthly_report_{report_year}_{report_month:02d}.xlsx"
            
            if options['save_path']:
                file_path = f"{options['save_path']}/{filename}"
                self.save_report_to_file(report_data, file_path)
                self.stdout.write(
                    self.style.SUCCESS(f'Report saved to: {file_path}')
                )
            
            # Email report if requested
            if options['email']:
                self.email_report(report_data, filename, options['email'], start_date, end_date)
                self.stdout.write(
                    self.style.SUCCESS(f'Report emailed to: {", ".join(options["email"])}')
                )
            
            # Print summary
            self.print_summary(report_data)
            
        except Exception as e:
            raise CommandError(f'Error generating report: {str(e)}')

    def generate_report_data(self, start_date, end_date):
        """Generate comprehensive monthly report data"""
        
        # Payment summary
        payments = StudentPayment.objects.filter(
            payment_date__range=[start_date, end_date],
            status='Confirmed'
        ).select_related('enrollment__student', 'enrollment__session__program')
        
        # Collections by program
        program_collections = {}
        total_collected = Decimal('0')
        
        for payment in payments:
            program_name = payment.enrollment.session.program.name
            if program_name not in program_collections:
                program_collections[program_name] = {
                    'amount': Decimal('0'),
                    'count': 0,
                    'students': set()
                }
            
            program_collections[program_name]['amount'] += payment.amount
            program_collections[program_name]['count'] += 1
            program_collections[program_name]['students'].add(payment.enrollment.student.student_id)
            total_collected += payment.amount
        
        # Collections by payment method
        method_collections = {}
        for payment in payments:
            method = payment.payment_method
            if method not in method_collections:
                method_collections[method] = {'amount': Decimal('0'), 'count': 0}
            method_collections[method]['amount'] += payment.amount
            method_collections[method]['count'] += 1
        
        # Outstanding balances
        outstanding_enrollments = StudentEnrollment.objects.filter(
            balance__gt=0,
            status__in=['Enrolled', 'Active']
        ).select_related('student', 'session__program')
        
        outstanding_by_program = {}
        total_outstanding = Decimal('0')
        
        for enrollment in outstanding_enrollments:
            program_name = enrollment.session.program.name
            if program_name not in outstanding_by_program:
                outstanding_by_program[program_name] = {
                    'amount': Decimal('0'),
                    'count': 0
                }
            
            outstanding_by_program[program_name]['amount'] += enrollment.balance
            outstanding_by_program[program_name]['count'] += 1
            total_outstanding += enrollment.balance
        
        # New enrollments in the month
        new_enrollments = StudentEnrollment.objects.filter(
            enrollment_date__range=[start_date, end_date]
        ).count()
        
        return {
            'period': {
                'start_date': start_date,
                'end_date': end_date,
                'month_name': start_date.strftime('%B %Y')
            },
            'collections': {
                'total_amount': total_collected,
                'total_count': payments.count(),
                'by_program': program_collections,
                'by_method': method_collections
            },
            'outstanding': {
                'total_amount': total_outstanding,
                'by_program': outstanding_by_program
            },
            'enrollments': {
                'new_count': new_enrollments
            }
        }

    def save_report_to_file(self, report_data, file_path):
        """Save report data to Excel file"""
        
        # Prepare DataFrames
        data_frames = {}
        
        # Collections summary
        collections_data = []
        for program, data in report_data['collections']['by_program'].items():
            collections_data.append({
                'Program': program,
                'Amount Collected': float(data['amount']),
                'Number of Payments': data['count'],
                'Number of Students': len(data['students']),
                'Average per Student': float(data['amount'] / len(data['students'])) if data['students'] else 0
            })
        
        if collections_data:
            collections_df = pd.DataFrame(collections_data)
            collections_df = ReportGenerator.add_summary_row(
                collections_df, 
                ['Amount Collected', 'Number of Payments', 'Number of Students']
            )
            data_frames['Collections by Program'] = collections_df
        
        # Payment methods summary
        methods_data = []
        for method, data in report_data['collections']['by_method'].items():
            methods_data.append({
                'Payment Method': method,
                'Amount': float(data['amount']),
                'Count': data['count']
            })
        
        if methods_data:
            methods_df = pd.DataFrame(methods_data)
            methods_df = ReportGenerator.add_summary_row(methods_df, ['Amount', 'Count'])
            data_frames['Payment Methods'] = methods_df
        
        # Outstanding balances
        outstanding_data = []
        for program, data in report_data['outstanding']['by_program'].items():
            outstanding_data.append({
                'Program': program,
                'Outstanding Amount': float(data['amount']),
                'Number of Students': data['count']
            })
        
        if outstanding_data:
            outstanding_df = pd.DataFrame(outstanding_data)
            outstanding_df = ReportGenerator.add_summary_row(
                outstanding_df, 
                ['Outstanding Amount', 'Number of Students']
            )
            data_frames['Outstanding Balances'] = outstanding_df
        
        # Save to file
        with pd.ExcelWriter(file_path, engine='xlsxwriter') as writer:
            for sheet_name, df in data_frames.items():
                df.to_excel(writer, sheet_name=sheet_name, index=False)

    def email_report(self, report_data, filename, email_addresses, start_date, end_date):
        """Email the report to specified addresses"""
        
        subject = f"Monthly Financial Report - {report_data['period']['month_name']}"
        
        # Create email body
        body = f"""
Dear Team,

Please find attached the monthly financial report for {report_data['period']['month_name']}.

Summary:
- Total Collections: PKR {report_data['collections']['total_amount']:,.2f}
- Number of Payments: {report_data['collections']['total_count']:,}
- Total Outstanding: PKR {report_data['outstanding']['total_amount']:,.2f}
- New Enrollments: {report_data['enrollments']['new_count']:,}

Report Period: {start_date.strftime('%d/%m/%Y')} to {end_date.strftime('%d/%m/%Y')}

Best regards,
GIHD SMS System
        """
        
        # Create and send email
        email = EmailMessage(
            subject=subject,
            body=body,
            from_email=settings.DEFAULT_FROM_EMAIL,
            to=email_addresses
        )
        
        # Generate and attach Excel file
        data_frames = {}
        # ... (same DataFrame generation as in save_report_to_file)
        
        import io
        buffer = io.BytesIO()
        with pd.ExcelWriter(buffer, engine='xlsxwriter') as writer:
            # Add summary sheet first
            summary_data = [{
                'Metric': 'Total Collections',
                'Value': f"PKR {report_data['collections']['total_amount']:,.2f}"
            }, {
                'Metric': 'Number of Payments',
                'Value': str(report_data['collections']['total_count'])
            }, {
                'Metric': 'Total Outstanding',
                'Value': f"PKR {report_data['outstanding']['total_amount']:,.2f}"
            }, {
                'Metric': 'New Enrollments',
                'Value': str(report_data['enrollments']['new_count'])
            }]
            
            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='Summary', index=False)
        
        buffer.seek(0)
        email.attach(filename, buffer.getvalue(), 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        email.send()

    def print_summary(self, report_data):
        """Print report summary to console"""
        
        self.stdout.write('\n' + '='*50)
        self.stdout.write(f"MONTHLY REPORT - {report_data['period']['month_name']}")
        self.stdout.write('='*50)
        
        self.stdout.write(f"\nCOLLECTIONS:")
        self.stdout.write(f"Total Amount: PKR {report_data['collections']['total_amount']:,.2f}")
        self.stdout.write(f"Number of Payments: {report_data['collections']['total_count']:,}")
        
        self.stdout.write(f"\nOUTSTANDING BALANCES:")
        self.stdout.write(f"Total Outstanding: PKR {report_data['outstanding']['total_amount']:,.2f}")
        
        self.stdout.write(f"\nENROLLMENTS:")
        self.stdout.write(f"New Enrollments: {report_data['enrollments']['new_count']:,}")
        
        self.stdout.write(f"\nTOP 3 PROGRAMS BY COLLECTIONS:")
        sorted_programs = sorted(
            report_data['collections']['by_program'].items(),
            key=lambda x: x[1]['amount'],
            reverse=True
        )[:3]
        
        for i, (program, data) in enumerate(sorted_programs, 1):
            self.stdout.write(f"{i}. {program}: PKR {data['amount']:,.2f}")
        
        self.stdout.write('\n' + '='*50)