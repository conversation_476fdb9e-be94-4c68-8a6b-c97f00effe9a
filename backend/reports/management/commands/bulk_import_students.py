from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.contrib.auth.models import User
import pandas as pd

from students.models import Student
from reports.utils import ImportService, DataValidator


class Command(BaseCommand):
    help = 'Bulk import students from CSV/Excel file'

    def add_arguments(self, parser):
        parser.add_argument(
            'file_path',
            type=str,
            help='Path to the CSV/Excel file containing student data'
        )
        parser.add_argument(
            '--created-by',
            type=str,
            help='Username of the user who should be marked as creator (defaults to admin)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Validate data without actually importing'
        )
        parser.add_argument(
            '--skip-duplicates',
            action='store_true',
            help='Skip students with duplicate CNICs instead of failing'
        )

    def handle(self, *args, **options):
        file_path = options['file_path']
        
        self.stdout.write(f'Reading file: {file_path}')
        
        try:
            # Read file
            with open(file_path, 'rb') as file:
                df = ImportService.read_file(file)
            
            self.stdout.write(f'Found {len(df)} records')
            
            # Get created_by user
            created_by_username = options.get('created_by', 'admin')
            try:
                created_by = User.objects.get(username=created_by_username)
            except User.DoesNotExist:
                raise CommandError(f'User "{created_by_username}" not found')
            
            # Define field mappings
            field_mappings = {
                'first_name': {'source_field': 'first_name', 'type': 'str', 'required': True},
                'last_name': {'source_field': 'last_name', 'type': 'str', 'required': True},
                'father_name': {'source_field': 'father_name', 'type': 'str', 'required': True},
                'date_of_birth': {'source_field': 'date_of_birth', 'type': 'date', 'required': True},
                'gender': {'source_field': 'gender', 'type': 'str', 'required': True},
                'cnic': {'source_field': 'cnic', 'type': 'str', 'required': False},
                'phone_number': {'source_field': 'phone_number', 'type': 'str', 'required': True},
                'email': {'source_field': 'email', 'type': 'str', 'required': False},
                'address_line_1': {'source_field': 'address_line_1', 'type': 'str', 'required': True},
                'city': {'source_field': 'city', 'type': 'str', 'required': True},
                'province': {'source_field': 'province', 'type': 'str', 'required': True},
                'religion': {'source_field': 'religion', 'type': 'str', 'default': 'Islam'},
                'nationality': {'source_field': 'nationality', 'type': 'str', 'default': 'Pakistani'},
            }
            
            # Clean and validate data
            self.stdout.write('Validating data...')
            cleaned_data, errors = ImportService.clean_data(df, field_mappings)
            
            if errors:
                self.stdout.write(self.style.ERROR('Validation errors found:'))
                for error in errors[:10]:  # Show first 10 errors
                    self.stdout.write(self.style.ERROR(f'  - {error}'))
                if len(errors) > 10:
                    self.stdout.write(self.style.ERROR(f'  ... and {len(errors) - 10} more errors'))
                raise CommandError('Data validation failed')
            
            # Additional validation
            self.stdout.write('Performing additional validation...')
            validation_errors = []
            
            for i, row_data in enumerate(cleaned_data):
                row_num = i + 1
                
                # Validate CNIC format
                if row_data.get('cnic') and not DataValidator.validate_cnic(row_data['cnic']):
                    validation_errors.append(f'Row {row_num}: Invalid CNIC format: {row_data["cnic"]}')
                
                # Validate phone number
                if not DataValidator.validate_phone(row_data['phone_number']):
                    validation_errors.append(f'Row {row_num}: Invalid phone number: {row_data["phone_number"]}')
                
                # Validate email
                if row_data.get('email') and not DataValidator.validate_email(row_data['email']):
                    validation_errors.append(f'Row {row_num}: Invalid email: {row_data["email"]}')
                
                # Validate gender
                if row_data['gender'].upper() not in ['M', 'F', 'O']:
                    validation_errors.append(f'Row {row_num}: Invalid gender: {row_data["gender"]}')
                    
                # Validate province
                valid_provinces = [
                    'Punjab', 'Sindh', 'KPK', 'Balochistan', 
                    'Gilgit-Baltistan', 'AJK', 'Islamabad'
                ]
                if row_data['province'] not in valid_provinces:
                    validation_errors.append(f'Row {row_num}: Invalid province: {row_data["province"]}')
                
                # Check for duplicate CNIC in file
                if row_data.get('cnic'):
                    duplicate_rows = [
                        j for j, other_row in enumerate(cleaned_data) 
                        if j != i and other_row.get('cnic') == row_data['cnic']
                    ]
                    if duplicate_rows:
                        validation_errors.append(
                            f'Row {row_num}: Duplicate CNIC {row_data["cnic"]} found in rows: {[j+1 for j in duplicate_rows]}'
                        )
            
            if validation_errors:
                self.stdout.write(self.style.ERROR('Additional validation errors:'))
                for error in validation_errors[:10]:
                    self.stdout.write(self.style.ERROR(f'  - {error}'))
                if len(validation_errors) > 10:
                    self.stdout.write(self.style.ERROR(f'  ... and {len(validation_errors) - 10} more errors'))
                raise CommandError('Additional validation failed')
            
            # Check for existing students with same CNIC
            duplicate_cnics = []
            for row_data in cleaned_data:
                if row_data.get('cnic') and Student.objects.filter(cnic=row_data['cnic']).exists():
                    duplicate_cnics.append(row_data['cnic'])
            
            if duplicate_cnics and not options['skip_duplicates']:
                self.stdout.write(self.style.ERROR(f'Found {len(duplicate_cnics)} students with existing CNICs:'))
                for cnic in duplicate_cnics[:5]:
                    self.stdout.write(self.style.ERROR(f'  - {cnic}'))
                if len(duplicate_cnics) > 5:
                    self.stdout.write(self.style.ERROR(f'  ... and {len(duplicate_cnics) - 5} more'))
                raise CommandError('Use --skip-duplicates to skip existing students')
            
            # Dry run - just validate and exit
            if options['dry_run']:
                self.stdout.write(self.style.SUCCESS(f'Dry run completed successfully!'))
                self.stdout.write(f'Found {len(cleaned_data)} valid records')
                if duplicate_cnics:
                    self.stdout.write(f'{len(duplicate_cnics)} records would be skipped due to duplicate CNICs')
                return
            
            # Import students
            self.stdout.write('Importing students...')
            imported_count = 0
            skipped_count = 0
            
            with transaction.atomic():
                for row_data in cleaned_data:
                    try:
                        # Skip if duplicate CNIC and skip_duplicates is True
                        if (row_data.get('cnic') and 
                            Student.objects.filter(cnic=row_data['cnic']).exists() and 
                            options['skip_duplicates']):
                            skipped_count += 1
                            continue
                        
                        # Normalize gender
                        row_data['gender'] = row_data['gender'].upper()
                        
                        # Create student
                        student = Student.objects.create(
                            **row_data,
                            created_by=created_by
                        )
                        imported_count += 1
                        
                        if imported_count % 100 == 0:
                            self.stdout.write(f'Imported {imported_count} students...')
                        
                    except Exception as e:
                        raise CommandError(f'Error importing student {row_data.get("first_name", "")} {row_data.get("last_name", "")}: {str(e)}')
            
            # Success summary
            self.stdout.write(self.style.SUCCESS(f'\nImport completed successfully!'))
            self.stdout.write(f'Imported: {imported_count} students')
            if skipped_count > 0:
                self.stdout.write(f'Skipped: {skipped_count} students (duplicate CNICs)')
            self.stdout.write(f'Total processed: {imported_count + skipped_count} records')
            
        except FileNotFoundError:
            raise CommandError(f'File not found: {file_path}')
        except Exception as e:
            raise CommandError(f'Error: {str(e)}')