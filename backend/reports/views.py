import pandas as pd
from django.shortcuts import render
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.db import transaction
from django.core.exceptions import ValidationError
from django.utils.decorators import method_decorator
from django.views import View
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from datetime import datetime, date, timedelta
from decimal import Decimal

from students.models import Student, Guardian
from academics.models import AcademicYear, Department, Program, Session, Campus
from fees.models import (
    StudentEnrollment, StudentPayment, FeeStructure, 
    StudentRefund, FeeInstallment, Scholarship
)
from documents.models import StudentDocument, DocumentReminder
from .utils import (
    ExportService, ImportService, ReportGenerator, 
    BankReconciliationService, DataValidator
)


# IMPORT VIEWS
@method_decorator([login_required, csrf_exempt], name='dispatch')
class StudentImportView(View):
    """Import students from CSV/Excel files"""
    
    def get(self, request):
        return render(request, 'reports/import_students.html')
    
    def post(self, request):
        try:
            file = request.FILES.get('file')
            if not file:
                return JsonResponse({'error': 'No file provided'}, status=400)
            
            # Read and validate file
            df = ImportService.read_file(file)
            
            # Define field mappings for student import
            field_mappings = {
                'first_name': {'source_field': 'first_name', 'type': 'str', 'required': True},
                'last_name': {'source_field': 'last_name', 'type': 'str', 'required': True},
                'father_name': {'source_field': 'father_name', 'type': 'str', 'required': True},
                'date_of_birth': {'source_field': 'date_of_birth', 'type': 'date', 'required': True},
                'gender': {'source_field': 'gender', 'type': 'str', 'required': True},
                'cnic': {'source_field': 'cnic', 'type': 'str', 'required': False},
                'phone_number': {'source_field': 'phone_number', 'type': 'str', 'required': True},
                'email': {'source_field': 'email', 'type': 'str', 'required': False},
                'address_line_1': {'source_field': 'address_line_1', 'type': 'str', 'required': True},
                'city': {'source_field': 'city', 'type': 'str', 'required': True},
                'province': {'source_field': 'province', 'type': 'str', 'required': True},
                'religion': {'source_field': 'religion', 'type': 'str', 'default': 'Islam'},
                'nationality': {'source_field': 'nationality', 'type': 'str', 'default': 'Pakistani'},
            }
            
            # Clean and validate data
            cleaned_data, errors = ImportService.clean_data(df, field_mappings)
            
            if errors:
                return JsonResponse({'errors': errors}, status=400)
            
            # Import students
            imported_count = 0
            import_errors = []
            
            with transaction.atomic():
                for row_data in cleaned_data:
                    try:
                        # Additional validation
                        if row_data.get('cnic') and not DataValidator.validate_cnic(row_data['cnic']):
                            raise ValidationError(f"Invalid CNIC format: {row_data['cnic']}")
                        
                        if not DataValidator.validate_phone(row_data['phone_number']):
                            raise ValidationError(f"Invalid phone number: {row_data['phone_number']}")
                        
                        if row_data.get('email') and not DataValidator.validate_email(row_data['email']):
                            raise ValidationError(f"Invalid email: {row_data['email']}")
                        
                        # Check for duplicate
                        if row_data.get('cnic') and Student.objects.filter(cnic=row_data['cnic']).exists():
                            raise ValidationError(f"Student with CNIC {row_data['cnic']} already exists")
                        
                        # Create student
                        student = Student.objects.create(
                            **row_data,
                            created_by=request.user
                        )
                        imported_count += 1
                        
                    except Exception as e:
                        import_errors.append(f"Row {imported_count + 1}: {str(e)}")
            
            return JsonResponse({
                'success': True,
                'imported_count': imported_count,
                'errors': import_errors
            })
            
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)


@method_decorator([login_required, csrf_exempt], name='dispatch')
class PaymentImportView(View):
    """Import payment data from CSV/Excel files"""
    
    def get(self, request):
        return render(request, 'reports/import_payments.html')
    
    def post(self, request):
        try:
            file = request.FILES.get('file')
            if not file:
                return JsonResponse({'error': 'No file provided'}, status=400)
            
            df = ImportService.read_file(file)
            
            # Define field mappings for payment import
            field_mappings = {
                'student_id': {'source_field': 'student_id', 'type': 'str', 'required': True},
                'amount': {'source_field': 'amount', 'type': 'decimal', 'required': True},
                'payment_method': {'source_field': 'payment_method', 'type': 'str', 'required': True},
                'payment_date': {'source_field': 'payment_date', 'type': 'date', 'required': True},
                'reference_number': {'source_field': 'reference_number', 'type': 'str', 'required': False},
                'bank_name': {'source_field': 'bank_name', 'type': 'str', 'required': False},
                'remarks': {'source_field': 'remarks', 'type': 'str', 'required': False},
            }
            
            cleaned_data, errors = ImportService.clean_data(df, field_mappings)
            
            if errors:
                return JsonResponse({'errors': errors}, status=400)
            
            # Import payments
            imported_count = 0
            import_errors = []
            
            with transaction.atomic():
                for row_data in cleaned_data:
                    try:
                        # Find student
                        student = Student.objects.get(student_id=row_data['student_id'])
                        
                        # Find active enrollment
                        enrollment = StudentEnrollment.objects.filter(
                            student=student,
                            status__in=['Enrolled', 'Active']
                        ).first()
                        
                        if not enrollment:
                            raise ValidationError(f"No active enrollment found for student {row_data['student_id']}")
                        
                        # Create payment
                        payment = StudentPayment.objects.create(
                            enrollment=enrollment,
                            amount=row_data['amount'],
                            payment_method=row_data['payment_method'],
                            payment_date=row_data['payment_date'],
                            reference_number=row_data.get('reference_number'),
                            bank_name=row_data.get('bank_name'),
                            remarks=row_data.get('remarks'),
                            status='Confirmed',  # Auto-confirm imported payments
                            confirmed_by=request.user,
                            confirmation_date=datetime.now(),
                            created_by=request.user
                        )
                        imported_count += 1
                        
                    except Student.DoesNotExist:
                        import_errors.append(f"Student with ID {row_data['student_id']} not found")
                    except Exception as e:
                        import_errors.append(f"Row {imported_count + 1}: {str(e)}")
            
            return JsonResponse({
                'success': True,
                'imported_count': imported_count,
                'errors': import_errors
            })
            
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)


# EXPORT VIEWS
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def export_student_ledgers(request):
    """Export student ledgers"""
    try:
        # Get query parameters
        program_id = request.GET.get('program_id')
        session_id = request.GET.get('session_id')
        status = request.GET.get('status')
        export_format = request.GET.get('format', 'excel')
        
        # Build queryset
        queryset = StudentEnrollment.objects.select_related(
            'student', 'session', 'session__program', 'fee_structure'
        ).prefetch_related('payments')
        
        if program_id:
            queryset = queryset.filter(session__program_id=program_id)
        if session_id:
            queryset = queryset.filter(session_id=session_id)
        if status:
            queryset = queryset.filter(status=status)
        
        # Prepare data
        ledger_data = []
        for enrollment in queryset:
            payments = enrollment.payments.filter(status='Confirmed')
            
            ledger_data.append({
                'Student ID': enrollment.student.student_id,
                'Student Name': enrollment.student.get_full_name(),
                'Father Name': enrollment.student.father_name,
                'Program': enrollment.session.program.name,
                'Session': enrollment.session.name,
                'Total Fee': float(enrollment.total_fee),
                'Scholarship Amount': float(enrollment.scholarship_amount),
                'Net Fee': float(enrollment.net_fee),
                'Total Paid': float(enrollment.total_paid),
                'Balance': float(enrollment.balance),
                'Status': enrollment.status,
                'Last Payment Date': payments.last().payment_date if payments.exists() else '',
                'Phone': enrollment.student.phone_number,
                'City': enrollment.student.city,
            })
        
        df = pd.DataFrame(ledger_data)
        
        if df.empty:
            return Response({'error': 'No data found'}, status=404)
        
        # Add summary row
        numeric_columns = ['Total Fee', 'Scholarship Amount', 'Net Fee', 'Total Paid', 'Balance']
        df = ReportGenerator.add_summary_row(df, numeric_columns)
        
        filename = f"student_ledgers_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        if export_format == 'csv':
            return ExportService.create_csv_response(f"{filename}.csv", df)
        else:
            return ExportService.create_excel_response(
                f"{filename}.xlsx",
                {'Student Ledgers': df}
            )
        
    except Exception as e:
        return Response({'error': str(e)}, status=500)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def export_class_rosters(request):
    """Export class rosters"""
    try:
        session_id = request.GET.get('session_id')
        program_id = request.GET.get('program_id')
        export_format = request.GET.get('format', 'excel')
        
        if not session_id and not program_id:
            return Response({'error': 'session_id or program_id is required'}, status=400)
        
        # Build queryset
        queryset = StudentEnrollment.objects.filter(
            status__in=['Enrolled', 'Active']
        ).select_related('student', 'session', 'session__program')
        
        if session_id:
            queryset = queryset.filter(session_id=session_id)
        if program_id:
            queryset = queryset.filter(session__program_id=program_id)
        
        # Group by session
        sessions_data = {}
        for enrollment in queryset:
            session_name = enrollment.session.name
            if session_name not in sessions_data:
                sessions_data[session_name] = []
            
            sessions_data[session_name].append({
                'Student ID': enrollment.student.student_id,
                'Student Name': enrollment.student.get_full_name(),
                'Father Name': enrollment.student.father_name,
                'CNIC': enrollment.student.cnic or '',
                'Phone': enrollment.student.phone_number,
                'Email': enrollment.student.email or '',
                'Address': enrollment.student.address_line_1,
                'City': enrollment.student.city,
                'Province': enrollment.student.province,
                'Enrollment Date': enrollment.enrollment_date.strftime('%d/%m/%Y'),
                'Status': enrollment.status,
            })
        
        if not sessions_data:
            return Response({'error': 'No data found'}, status=404)
        
        filename = f"class_rosters_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        if export_format == 'csv':
            # For CSV, combine all sessions
            all_data = []
            for session_name, students in sessions_data.items():
                for student in students:
                    student['Session'] = session_name
                    all_data.append(student)
            df = pd.DataFrame(all_data)
            return ExportService.create_csv_response(f"{filename}.csv", df)
        else:
            # For Excel, create separate sheets for each session
            data_frames = {}
            for session_name, students in sessions_data.items():
                df = pd.DataFrame(students)
                data_frames[session_name[:31]] = df  # Excel sheet name limit
            
            return ExportService.create_excel_response(f"{filename}.xlsx", data_frames)
        
    except Exception as e:
        return Response({'error': str(e)}, status=500)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def export_financial_summary(request):
    """Export financial summary reports"""
    try:
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')
        program_id = request.GET.get('program_id')
        export_format = request.GET.get('format', 'excel')
        
        # Default to current academic year if no dates provided
        if not start_date or not end_date:
            current_year = AcademicYear.objects.filter(is_active=True).first()
            if current_year:
                start_date = current_year.start_date
                end_date = current_year.end_date
            else:
                start_date = date.today().replace(month=1, day=1)
                end_date = date.today().replace(month=12, day=31)
        else:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
        
        # Payment Summary
        payment_queryset = StudentPayment.objects.filter(
            payment_date__range=[start_date, end_date],
            status='Confirmed'
        ).select_related('enrollment__student', 'enrollment__session__program')
        
        if program_id:
            payment_queryset = payment_queryset.filter(enrollment__session__program_id=program_id)
        
        # Group by program
        program_summary = {}
        for payment in payment_queryset:
            program_name = payment.enrollment.session.program.name
            if program_name not in program_summary:
                program_summary[program_name] = {
                    'total_collected': Decimal('0'),
                    'payment_count': 0,
                    'students': set()
                }
            
            program_summary[program_name]['total_collected'] += payment.amount
            program_summary[program_name]['payment_count'] += 1
            program_summary[program_name]['students'].add(payment.enrollment.student.student_id)
        
        # Convert to DataFrame
        summary_data = []
        for program, data in program_summary.items():
            summary_data.append({
                'Program': program,
                'Total Collected': float(data['total_collected']),
                'Number of Payments': data['payment_count'],
                'Number of Students': len(data['students']),
                'Average per Student': float(data['total_collected'] / len(data['students'])) if data['students'] else 0
            })
        
        # Outstanding balances
        outstanding_queryset = StudentEnrollment.objects.filter(
            balance__gt=0,
            status__in=['Enrolled', 'Active']
        ).select_related('student', 'session__program')
        
        if program_id:
            outstanding_queryset = outstanding_queryset.filter(session__program_id=program_id)
        
        outstanding_data = []
        for enrollment in outstanding_queryset:
            outstanding_data.append({
                'Student ID': enrollment.student.student_id,
                'Student Name': enrollment.student.get_full_name(),
                'Program': enrollment.session.program.name,
                'Session': enrollment.session.name,
                'Outstanding Balance': float(enrollment.balance),
                'Due Date': enrollment.fee_due_date.strftime('%d/%m/%Y'),
                'Days Overdue': (date.today() - enrollment.fee_due_date).days if date.today() > enrollment.fee_due_date else 0,
                'Phone': enrollment.student.phone_number,
            })
        
        filename = f"financial_summary_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}"
        
        if export_format == 'csv':
            # Combine all data for CSV
            combined_data = summary_data + outstanding_data
            df = pd.DataFrame(combined_data)
            return ExportService.create_csv_response(f"{filename}.csv", df)
        else:
            data_frames = {}
            if summary_data:
                summary_df = pd.DataFrame(summary_data)
                summary_df = ReportGenerator.add_summary_row(summary_df, ['Total Collected', 'Number of Payments', 'Number of Students'])
                data_frames['Program Summary'] = summary_df
            
            if outstanding_data:
                outstanding_df = pd.DataFrame(outstanding_data)
                outstanding_df = ReportGenerator.add_summary_row(outstanding_df, ['Outstanding Balance'])
                data_frames['Outstanding Balances'] = outstanding_df
            
            return ExportService.create_excel_response(f"{filename}.xlsx", data_frames)
        
    except Exception as e:
        return Response({'error': str(e)}, status=500)


# REPORTING VIEWS
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def daily_collection_report(request):
    """Generate daily collection report"""
    try:
        report_date = request.GET.get('date', date.today())
        if isinstance(report_date, str):
            report_date = datetime.strptime(report_date, '%Y-%m-%d').date()
        
        payments = StudentPayment.objects.filter(
            payment_date=report_date,
            status='Confirmed'
        ).select_related('enrollment__student', 'enrollment__session__program')
        
        # Summary by payment method
        method_summary = {}
        for payment in payments:
            method = payment.payment_method
            if method not in method_summary:
                method_summary[method] = {'count': 0, 'amount': Decimal('0')}
            method_summary[method]['count'] += 1
            method_summary[method]['amount'] += payment.amount
        
        # Summary by program
        program_summary = {}
        for payment in payments:
            program = payment.enrollment.session.program.name
            if program not in program_summary:
                program_summary[program] = {'count': 0, 'amount': Decimal('0')}
            program_summary[program]['count'] += 1
            program_summary[program]['amount'] += payment.amount
        
        total_amount = sum(payment.amount for payment in payments)
        total_count = payments.count()
        
        return Response({
            'date': report_date,
            'total_amount': float(total_amount),
            'total_count': total_count,
            'method_summary': {
                method: {
                    'count': data['count'],
                    'amount': float(data['amount'])
                }
                for method, data in method_summary.items()
            },
            'program_summary': {
                program: {
                    'count': data['count'],
                    'amount': float(data['amount'])
                }
                for program, data in program_summary.items()
            }
        })
        
    except Exception as e:
        return Response({'error': str(e)}, status=500)


# BANK RECONCILIATION
@method_decorator([login_required, csrf_exempt], name='dispatch')
class BankReconciliationView(View):
    """Bank reconciliation functionality"""
    
    def get(self, request):
        return render(request, 'reports/bank_reconciliation.html')
    
    def post(self, request):
        try:
            file = request.FILES.get('bank_statement')
            if not file:
                return JsonResponse({'error': 'No bank statement file provided'}, status=400)
            
            # Parse bank statement
            bank_df = BankReconciliationService.parse_bank_statement(file)
            
            # Get unreconciled payments from the same period
            start_date = bank_df['date'].min().date()
            end_date = bank_df['date'].max().date()
            
            payments = StudentPayment.objects.filter(
                payment_date__range=[start_date, end_date],
                status='Confirmed'
            )
            
            # Match payments
            matched_payments, unmatched_entries = BankReconciliationService.match_payments(
                bank_df, payments
            )
            
            # Prepare response data
            matched_data = []
            for match in matched_payments:
                matched_data.append({
                    'bank_date': match['bank_entry']['date'].strftime('%Y-%m-%d'),
                    'bank_amount': float(match['bank_entry'].get('credit', 0)),
                    'bank_description': match['bank_entry']['description'],
                    'payment_id': match['payment'].payment_id,
                    'student_name': match['payment'].enrollment.student.get_full_name(),
                    'payment_amount': float(match['payment'].amount),
                    'payment_date': match['payment'].payment_date.strftime('%Y-%m-%d'),
                })
            
            unmatched_data = []
            for entry in unmatched_entries:
                unmatched_data.append({
                    'date': entry['date'].strftime('%Y-%m-%d'),
                    'amount': float(entry.get('credit', 0)),
                    'description': entry['description'],
                    'reference': entry.get('reference', ''),
                })
            
            return JsonResponse({
                'success': True,
                'matched_count': len(matched_payments),
                'unmatched_count': len(unmatched_entries),
                'matched_payments': matched_data,
                'unmatched_entries': unmatched_data,
            })
            
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
