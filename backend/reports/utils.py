import pandas as pd
import io
import os
from datetime import datetime, date
from decimal import Decimal
from django.http import HttpResponse
from django.core.exceptions import ValidationError
from django.db import transaction
import xlsxwriter
from openpyxl import load_workbook


class ExportService:
    """Service for exporting data to various formats"""
    
    @staticmethod
    def create_excel_response(filename, data_frames):
        """Create HTTP response with Excel file containing multiple sheets"""
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        
        # Create Excel writer object
        buffer = io.BytesIO()
        with pd.ExcelWriter(buffer, engine='xlsxwriter') as writer:
            workbook = writer.book
            
            # Define formats
            header_format = workbook.add_format({
                'bold': True,
                'bg_color': '#4472C4',
                'font_color': 'white',
                'border': 1
            })
            
            currency_format = workbook.add_format({
                'num_format': 'PKR #,##0.00',
                'border': 1
            })
            
            date_format = workbook.add_format({
                'num_format': 'dd/mm/yyyy',
                'border': 1
            })
            
            for sheet_name, df in data_frames.items():
                df.to_excel(writer, sheet_name=sheet_name, index=False)
                worksheet = writer.sheets[sheet_name]
                
                # Apply header formatting
                for col_num, value in enumerate(df.columns.values):
                    worksheet.write(0, col_num, value, header_format)
                
                # Auto-adjust column widths
                for i, col in enumerate(df.columns):
                    max_length = max(
                        df[col].astype(str).map(len).max(),
                        len(str(col))
                    ) + 2
                    worksheet.set_column(i, i, min(max_length, 50))
                
                # Apply formatting to specific column types
                for col_num, col_name in enumerate(df.columns):
                    if any(keyword in col_name.lower() for keyword in ['amount', 'fee', 'paid', 'balance', 'pkr']):
                        worksheet.set_column(col_num, col_num, None, currency_format)
                    elif any(keyword in col_name.lower() for keyword in ['date', 'created', 'updated']):
                        worksheet.set_column(col_num, col_num, None, date_format)
        
        buffer.seek(0)
        response.write(buffer.getvalue())
        return response
    
    @staticmethod
    def create_csv_response(filename, dataframe):
        """Create HTTP response with CSV file"""
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        dataframe.to_csv(response, index=False)
        return response


class ImportService:
    """Service for importing data from various formats"""
    
    @staticmethod
    def validate_file(file):
        """Validate uploaded file"""
        if not file:
            raise ValidationError("No file provided")
        
        # Check file size (max 10MB)
        if file.size > 10 * 1024 * 1024:
            raise ValidationError("File size too large. Maximum 10MB allowed.")
        
        # Check file extension
        ext = os.path.splitext(file.name)[1].lower()
        if ext not in ['.csv', '.xlsx', '.xls']:
            raise ValidationError("Invalid file format. Only CSV and Excel files are supported.")
        
        return ext
    
    @staticmethod
    def read_file(file):
        """Read uploaded file and return DataFrame"""
        ext = ImportService.validate_file(file)
        
        try:
            if ext == '.csv':
                # Try different encodings
                try:
                    df = pd.read_csv(file, encoding='utf-8')
                except UnicodeDecodeError:
                    file.seek(0)
                    df = pd.read_csv(file, encoding='latin-1')
            else:  # Excel files
                df = pd.read_excel(file, engine='openpyxl')
            
            # Clean column names
            df.columns = df.columns.str.strip().str.lower().str.replace(' ', '_')
            
            return df
        except Exception as e:
            raise ValidationError(f"Error reading file: {str(e)}")
    
    @staticmethod
    def clean_data(df, field_mappings):
        """Clean and validate data according to field mappings"""
        cleaned_data = []
        errors = []
        
        for index, row in df.iterrows():
            try:
                cleaned_row = {}
                for field_name, field_config in field_mappings.items():
                    value = row.get(field_config.get('source_field', field_name))
                    
                    # Handle empty values
                    if pd.isna(value) or value == '':
                        if field_config.get('required', False):
                            raise ValidationError(f"Required field '{field_name}' is empty")
                        cleaned_row[field_name] = field_config.get('default')
                        continue
                    
                    # Apply data type conversion
                    field_type = field_config.get('type', 'str')
                    if field_type == 'date':
                        cleaned_row[field_name] = pd.to_datetime(value).date()
                    elif field_type == 'decimal':
                        cleaned_row[field_name] = Decimal(str(value))
                    elif field_type == 'int':
                        cleaned_row[field_name] = int(value)
                    elif field_type == 'bool':
                        cleaned_row[field_name] = str(value).lower() in ['true', '1', 'yes', 'y']
                    else:
                        cleaned_row[field_name] = str(value).strip()
                
                cleaned_data.append(cleaned_row)
            
            except Exception as e:
                errors.append(f"Row {index + 2}: {str(e)}")
        
        return cleaned_data, errors


class ReportGenerator:
    """Service for generating various reports"""
    
    @staticmethod
    def format_currency(amount):
        """Format amount as Pakistani Rupee"""
        if amount is None:
            return "PKR 0.00"
        return f"PKR {amount:,.2f}"
    
    @staticmethod
    def format_date(date_obj):
        """Format date in Pakistani format"""
        if not date_obj:
            return ""
        if isinstance(date_obj, str):
            return date_obj
        return date_obj.strftime("%d/%m/%Y")
    
    @staticmethod
    def add_summary_row(df, numeric_columns):
        """Add summary row to DataFrame"""
        summary_row = {}
        for col in df.columns:
            if col in numeric_columns:
                summary_row[col] = df[col].sum()
            elif col == df.columns[0]:  # First column
                summary_row[col] = "TOTAL"
            else:
                summary_row[col] = ""
        
        # Convert to DataFrame and append
        summary_df = pd.DataFrame([summary_row])
        return pd.concat([df, summary_df], ignore_index=True)


class BankReconciliationService:
    """Service for bank reconciliation operations"""
    
    @staticmethod
    def parse_bank_statement(file):
        """Parse bank statement file"""
        df = ImportService.read_file(file)
        
        # Common bank statement column mappings
        column_mappings = {
            'date': ['date', 'transaction_date', 'value_date'],
            'description': ['description', 'narration', 'particulars', 'details'],
            'debit': ['debit', 'debit_amount', 'withdrawal'],
            'credit': ['credit', 'credit_amount', 'deposit'],
            'balance': ['balance', 'running_balance'],
            'reference': ['reference', 'ref_no', 'transaction_id', 'cheque_no']
        }
        
        # Map columns
        mapped_columns = {}
        for target_col, possible_cols in column_mappings.items():
            for col in df.columns:
                if any(possible in col.lower() for possible in possible_cols):
                    mapped_columns[target_col] = col
                    break
        
        # Rename columns
        df = df.rename(columns=mapped_columns)
        
        # Clean and validate data
        required_columns = ['date', 'description']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValidationError(f"Missing required columns: {', '.join(missing_columns)}")
        
        # Convert data types
        df['date'] = pd.to_datetime(df['date'])
        if 'debit' in df.columns:
            df['debit'] = pd.to_numeric(df['debit'], errors='coerce').fillna(0)
        if 'credit' in df.columns:
            df['credit'] = pd.to_numeric(df['credit'], errors='coerce').fillna(0)
        
        return df
    
    @staticmethod
    def match_payments(bank_statement_df, payment_queryset):
        """Match bank statement entries with system payments"""
        matched_payments = []
        unmatched_entries = []
        
        for _, bank_entry in bank_statement_df.iterrows():
            # Try to match by amount and date (within 3 days)
            potential_matches = payment_queryset.filter(
                amount=bank_entry.get('credit', 0),
                payment_date__range=[
                    bank_entry['date'].date() - pd.Timedelta(days=3),
                    bank_entry['date'].date() + pd.Timedelta(days=3)
                ]
            )
            
            if potential_matches.exists():
                matched_payments.append({
                    'bank_entry': bank_entry,
                    'payment': potential_matches.first()
                })
            else:
                unmatched_entries.append(bank_entry)
        
        return matched_payments, unmatched_entries


class DataValidator:
    """Service for validating imported data"""
    
    @staticmethod
    def validate_cnic(cnic):
        """Validate Pakistani CNIC format"""
        import re
        if not cnic:
            return True  # Allow empty CNIC
        pattern = r'^\d{5}-\d{7}-\d{1}$'
        return re.match(pattern, str(cnic)) is not None
    
    @staticmethod
    def validate_phone(phone):
        """Validate Pakistani phone number format"""
        import re
        if not phone:
            return False
        pattern = r'^(\+92|0)\d{10}$'
        return re.match(pattern, str(phone)) is not None
    
    @staticmethod
    def validate_email(email):
        """Validate email format"""
        import re
        if not email:
            return True  # Allow empty email
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, str(email)) is not None