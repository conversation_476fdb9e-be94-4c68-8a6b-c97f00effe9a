from django.contrib import admin
from django.urls import reverse
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from django.db import models
from django.forms import TextInput, Textarea
from simple_history.admin import SimpleHistoryAdmin
from .models import (
    FeeCategory, FeeStructure, FeeStructureItem, Scholarship,
    StudentEnrollment, StudentPayment, FeeInstallment, RefundPolicy,
    StudentRefund, StudentTransfer, StudentAttendance, StudentGrade
)


@admin.register(FeeCategory)
class FeeCategoryAdmin(SimpleHistoryAdmin):
    list_display = ['name', 'category_type', 'is_mandatory', 'is_refundable', 
                   'allow_partial_payment', 'is_active']
    list_filter = ['category_type', 'is_mandatory', 'is_refundable', 
                  'allow_partial_payment', 'is_active', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['category_type', 'name']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'category_type', 'description')
        }),
        ('Settings', {
            'fields': ('is_mandatory', 'is_refundable', 'allow_partial_payment')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )


@admin.register(FeeStructure)
class FeeStructureAdmin(SimpleHistoryAdmin):
    list_display = ['name', 'program', 'academic_year', 'total_fee_display', 
                   'effective_from', 'effective_to', 'is_current', 'is_active']
    list_filter = ['program__department', 'academic_year', 'is_active', 'created_at']
    search_fields = ['name', 'program__name']
    readonly_fields = ['total_fee', 'created_at', 'updated_at']
    ordering = ['-effective_from', 'program']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'program', 'academic_year')
        }),
        ('Validity Period', {
            'fields': ('effective_from', 'effective_to')
        }),
        ('Fee Summary', {
            'fields': ('total_fee',)
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('System Information', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def total_fee_display(self, obj):
        """Display total fee in Pakistani Rupees"""
        return f"PKR {obj.total_fee:,.2f}"
    total_fee_display.short_description = "Total Fee"
    total_fee_display.admin_order_field = 'total_fee'
    
    def is_current(self, obj):
        """Display if fee structure is currently active"""
        if obj.is_current():
            return format_html('<span style="color: green; font-weight: bold;">✓ Current</span>')
        return format_html('<span style="color: gray;">Inactive</span>')
    is_current.short_description = "Current"
    
    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


class FeeStructureItemInline(admin.TabularInline):
    model = FeeStructureItem
    extra = 0
    fields = ['fee_category', 'amount', 'description', 'is_mandatory', 
             'is_one_time', 'installment_allowed', 'due_after_enrollment', 'is_active']
    readonly_fields = ['created_at', 'updated_at']


# Add inline to FeeStructure admin
FeeStructureAdmin.inlines = [FeeStructureItemInline]


@admin.register(FeeStructureItem)
class FeeStructureItemAdmin(SimpleHistoryAdmin):
    list_display = ['fee_structure', 'fee_category', 'amount_display', 'is_mandatory', 
                   'is_one_time', 'installment_allowed', 'due_after_enrollment', 'is_active']
    list_filter = ['fee_category__category_type', 'is_mandatory', 'is_one_time', 
                  'installment_allowed', 'is_active']
    search_fields = ['fee_structure__name', 'fee_category__name', 'description']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['fee_structure', 'fee_category']
    
    def amount_display(self, obj):
        """Display amount in Pakistani Rupees"""
        return f"PKR {obj.amount:,.2f}"
    amount_display.short_description = "Amount"
    amount_display.admin_order_field = 'amount'


@admin.register(Scholarship)
class ScholarshipAdmin(SimpleHistoryAdmin):
    list_display = ['name', 'scholarship_type', 'discount_display_admin', 'min_marks', 
                   'max_family_income_display', 'recipients_count', 'is_valid', 'is_active']
    list_filter = ['scholarship_type', 'discount_type', 'is_active', 'created_at']
    search_fields = ['name', 'description', 'eligibility_criteria']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['scholarship_type', 'name']
    filter_horizontal = ['applicable_categories']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'scholarship_type', 'description')
        }),
        ('Discount Settings', {
            'fields': ('discount_type', 'discount_percentage', 'discount_amount')
        }),
        ('Applicability', {
            'fields': ('applicable_categories',)
        }),
        ('Eligibility Criteria', {
            'fields': ('min_marks', 'max_family_income', 'eligibility_criteria')
        }),
        ('Validity', {
            'fields': ('valid_from', 'valid_to', 'max_recipients')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('System Information', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def discount_display_admin(self, obj):
        """Display discount in admin list"""
        return obj.get_discount_display()
    discount_display_admin.short_description = "Discount"
    
    def max_family_income_display(self, obj):
        """Display max family income"""
        if obj.max_family_income:
            return f"PKR {obj.max_family_income:,.2f}"
        return "No Limit"
    max_family_income_display.short_description = "Max Family Income"
    
    def recipients_count(self, obj):
        """Display current recipients count"""
        count = obj.studentenrollment_set.filter(status__in=['Enrolled', 'Active']).count()
        max_count = obj.max_recipients or "∞"
        return f"{count}/{max_count}"
    recipients_count.short_description = "Recipients"
    
    def is_valid(self, obj):
        """Display validity status"""
        if obj.is_valid():
            return format_html('<span style="color: green;">✓ Valid</span>')
        return format_html('<span style="color: red;">✗ Expired</span>')
    is_valid.short_description = "Valid"
    
    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(StudentEnrollment)
class StudentEnrollmentAdmin(SimpleHistoryAdmin):
    list_display = ['student_name', 'session', 'status', 'total_fee_display', 
                   'scholarship_amount_display', 'net_fee_display', 'balance_display', 
                   'payment_status', 'enrollment_date']
    list_filter = ['status', 'session__academic_year', 'session__program', 
                  'scholarship__scholarship_type', 'enrollment_date']
    search_fields = ['student__first_name', 'student__last_name', 'student__student_id']
    readonly_fields = ['total_fee', 'net_fee', 'total_paid', 'balance', 'created_at', 'updated_at']
    ordering = ['-enrollment_date']
    date_hierarchy = 'enrollment_date'
    
    fieldsets = (
        ('Enrollment Details', {
            'fields': ('student', 'session', 'fee_structure', 'status')
        }),
        ('Fee Structure', {
            'fields': ('total_fee', 'scholarship', 'scholarship_amount', 'net_fee')
        }),
        ('Payment Tracking', {
            'fields': ('total_paid', 'balance', 'fee_due_date')
        }),
        ('Important Dates', {
            'fields': ('enrollment_date', 'completion_date', 'withdrawal_date')
        }),
        ('System Information', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def student_name(self, obj):
        """Display student name with link"""
        if obj.student:
            url = reverse('admin:students_student_change', args=[obj.student.pk])
            return format_html('<a href="{}">{}</a>', url, obj.student.get_full_name())
        return "-"
    student_name.short_description = "Student"
    student_name.admin_order_field = 'student__first_name'
    
    def total_fee_display(self, obj):
        """Display total fee"""
        return f"PKR {obj.total_fee:,.2f}"
    total_fee_display.short_description = "Total Fee"
    total_fee_display.admin_order_field = 'total_fee'
    
    def scholarship_amount_display(self, obj):
        """Display scholarship amount"""
        if obj.scholarship_amount > 0:
            return format_html('<span style="color: green;">PKR {}</span>', f"{obj.scholarship_amount:,.2f}")
        return "PKR 0.00"
    scholarship_amount_display.short_description = "Scholarship"
    scholarship_amount_display.admin_order_field = 'scholarship_amount'
    
    def net_fee_display(self, obj):
        """Display net fee after scholarship"""
        return f"PKR {obj.net_fee:,.2f}"
    net_fee_display.short_description = "Net Fee"
    net_fee_display.admin_order_field = 'net_fee'
    
    def balance_display(self, obj):
        """Display balance with color coding"""
        if obj.balance <= 0:
            return format_html('<span style="color: green; font-weight: bold;">PKR 0.00</span>')
        elif obj.is_overdue():
            return format_html('<span style="color: red; font-weight: bold;">PKR {}</span>', f"{obj.balance:,.2f}")
        else:
            return format_html('<span style="color: orange;">PKR {}</span>', f"{obj.balance:,.2f}")
    balance_display.short_description = "Balance"
    balance_display.admin_order_field = 'balance'
    
    def payment_status(self, obj):
        """Display payment status"""
        if obj.is_fee_paid():
            return format_html('<span style="color: green;">✓ Paid</span>')
        elif obj.is_overdue():
            return format_html('<span style="color: red;">⚠ Overdue</span>')
        else:
            return format_html('<span style="color: orange;">⏳ Pending</span>')
    payment_status.short_description = "Payment Status"
    
    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(StudentPayment)
class StudentPaymentAdmin(SimpleHistoryAdmin):
    list_display = ['payment_id', 'student_name', 'amount_display', 'payment_method', 
                   'payment_date', 'status', 'confirmed_by', 'created_at']
    list_filter = ['payment_method', 'status', 'payment_date', 'created_at']
    search_fields = ['payment_id', 'enrollment__student__first_name', 
                    'enrollment__student__last_name', 'reference_number', 'bank_name']
    readonly_fields = ['payment_id', 'created_at', 'updated_at']
    ordering = ['-payment_date', '-created_at']
    date_hierarchy = 'payment_date'
    
    fieldsets = (
        ('Payment Details', {
            'fields': ('payment_id', 'enrollment', 'amount', 'payment_method', 'payment_date')
        }),
        ('Reference Information', {
            'fields': ('reference_number', 'bank_name', 'cheque_number')
        }),
        ('Status', {
            'fields': ('status', 'confirmation_date', 'confirmed_by')
        }),
        ('Notes', {
            'fields': ('remarks',)
        }),
        ('System Information', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def student_name(self, obj):
        """Display student name"""
        if obj.enrollment and obj.enrollment.student:
            return obj.enrollment.student.get_full_name()
        return "-"
    student_name.short_description = "Student"
    
    def amount_display(self, obj):
        """Display amount in Pakistani Rupees"""
        return f"PKR {obj.amount:,.2f}"
    amount_display.short_description = "Amount"
    amount_display.admin_order_field = 'amount'
    
    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.created_by = request.user
        if obj.status == 'Confirmed' and not obj.confirmed_by:
            obj.confirmed_by = request.user
            from django.utils import timezone
            obj.confirmation_date = timezone.now()
        super().save_model(request, obj, form, change)


@admin.register(FeeInstallment)
class FeeInstallmentAdmin(SimpleHistoryAdmin):
    list_display = ['enrollment', 'installment_number', 'amount_display', 'due_date', 
                   'paid_amount_display', 'remaining_display', 'is_paid', 'overdue_status']
    list_filter = ['is_paid', 'due_date']
    search_fields = ['enrollment__student__first_name', 'enrollment__student__last_name']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['enrollment', 'installment_number']
    
    def amount_display(self, obj):
        return f"PKR {obj.amount:,.2f}"
    amount_display.short_description = "Amount"
    
    def paid_amount_display(self, obj):
        return f"PKR {obj.paid_amount:,.2f}"
    paid_amount_display.short_description = "Paid"
    
    def remaining_display(self, obj):
        remaining = obj.remaining_amount()
        if remaining <= 0:
            return format_html('<span style="color: green;">PKR 0.00</span>')
        return f"PKR {remaining:,.2f}"
    remaining_display.short_description = "Remaining"
    
    def overdue_status(self, obj):
        if obj.is_overdue():
            return format_html('<span style="color: red;">⚠ Overdue</span>')
        elif obj.is_paid:
            return format_html('<span style="color: green;">✓ Paid</span>')
        else:
            return format_html('<span style="color: orange;">⏳ Pending</span>')
    overdue_status.short_description = "Status"


@admin.register(RefundPolicy)
class RefundPolicyAdmin(SimpleHistoryAdmin):
    list_display = ['name', 'days_after_enrollment', 'refund_percentage', 'is_active']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']
    filter_horizontal = ['programs', 'fee_categories']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'description')
        }),
        ('Refund Rules', {
            'fields': ('days_after_enrollment', 'refund_percentage')
        }),
        ('Applicability', {
            'fields': ('programs', 'fee_categories')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )


@admin.register(StudentRefund)
class StudentRefundAdmin(SimpleHistoryAdmin):
    list_display = ['refund_id', 'student_name', 'reason', 'requested_amount_display', 
                   'refund_amount_display', 'status', 'request_date', 'approved_by']
    list_filter = ['reason', 'status', 'request_date']
    search_fields = ['refund_id', 'enrollment__student__first_name', 
                    'enrollment__student__last_name']
    readonly_fields = ['refund_id', 'created_at', 'updated_at']
    ordering = ['-request_date']
    
    fieldsets = (
        ('Refund Details', {
            'fields': ('refund_id', 'enrollment', 'reason')
        }),
        ('Amounts', {
            'fields': ('requested_amount', 'refund_amount')
        }),
        ('Dates', {
            'fields': ('request_date', 'approval_date', 'processing_date')
        }),
        ('Status & Approval', {
            'fields': ('status', 'approved_by', 'processed_by')
        }),
        ('Notes', {
            'fields': ('request_notes', 'approval_notes', 'processing_notes')
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def student_name(self, obj):
        if obj.enrollment and obj.enrollment.student:
            return obj.enrollment.student.get_full_name()
        return "-"
    student_name.short_description = "Student"
    
    def requested_amount_display(self, obj):
        return f"PKR {obj.requested_amount:,.2f}"
    requested_amount_display.short_description = "Requested Amount"
    
    def refund_amount_display(self, obj):
        return f"PKR {obj.refund_amount:,.2f}"
    refund_amount_display.short_description = "Refund Amount"


@admin.register(StudentTransfer)
class StudentTransferAdmin(SimpleHistoryAdmin):
    list_display = ['transfer_id', 'student_name', 'transfer_type', 'from_session', 
                   'to_session', 'status', 'request_date', 'approved_by']
    list_filter = ['transfer_type', 'status', 'request_date']
    search_fields = ['transfer_id', 'student__first_name', 'student__last_name']
    readonly_fields = ['transfer_id', 'created_at', 'updated_at']
    ordering = ['-request_date']
    
    fieldsets = (
        ('Transfer Details', {
            'fields': ('transfer_id', 'student', 'transfer_type')
        }),
        ('From/To', {
            'fields': ('from_enrollment', 'to_session', 'to_fee_structure')
        }),
        ('Dates', {
            'fields': ('request_date', 'effective_date', 'approval_date', 'completion_date')
        }),
        ('Status & Approval', {
            'fields': ('status', 'requested_by', 'approved_by')
        }),
        ('Fee Adjustment', {
            'fields': ('fee_adjustment', 'adjustment_reason')
        }),
        ('Notes', {
            'fields': ('request_reason', 'approval_notes', 'rejection_reason')
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def student_name(self, obj):
        if obj.student:
            return obj.student.get_full_name()
        return "-"
    student_name.short_description = "Student"
    
    def from_session(self, obj):
        if obj.from_enrollment:
            return obj.from_enrollment.session.name
        return "-"
    from_session.short_description = "From Session"


@admin.register(StudentAttendance)
class StudentAttendanceAdmin(SimpleHistoryAdmin):
    list_display = ['student_name', 'session_course', 'date', 'status', 'arrival_time', 'recorded_by']
    list_filter = ['status', 'date', 'session_course__session__academic_year']
    search_fields = ['enrollment__student__first_name', 'enrollment__student__last_name']
    readonly_fields = ['recorded_at', 'updated_at']
    ordering = ['-date', 'enrollment']
    date_hierarchy = 'date'
    
    def student_name(self, obj):
        if obj.enrollment and obj.enrollment.student:
            return obj.enrollment.student.get_full_name()
        return "-"
    student_name.short_description = "Student"


@admin.register(StudentGrade)
class StudentGradeAdmin(SimpleHistoryAdmin):
    list_display = ['student_name', 'session_course', 'grade_type', 'assessment_name', 
                   'marks_display', 'percentage', 'letter_grade', 'assessment_date']
    list_filter = ['grade_type', 'letter_grade', 'assessment_date', 
                  'session_course__session__academic_year']
    search_fields = ['enrollment__student__first_name', 'enrollment__student__last_name', 
                    'assessment_name']
    readonly_fields = ['percentage', 'letter_grade', 'gpa_points', 'recorded_at', 'updated_at']
    ordering = ['-assessment_date', 'enrollment']
    
    def student_name(self, obj):
        if obj.enrollment and obj.enrollment.student:
            return obj.enrollment.student.get_full_name()
        return "-"
    student_name.short_description = "Student"
    
    def marks_display(self, obj):
        return f"{obj.marks_obtained}/{obj.total_marks}"
    marks_display.short_description = "Marks"


# Custom admin actions
@admin.action(description='Mark selected payments as confirmed')
def confirm_payments(modeladmin, request, queryset):
    """Confirm selected payments"""
    from django.utils import timezone
    queryset.update(
        status='Confirmed',
        confirmed_by=request.user,
        confirmation_date=timezone.now()
    )


@admin.action(description='Mark selected enrollments as active')
def activate_enrollments(modeladmin, request, queryset):
    """Activate selected enrollments"""
    queryset.update(status='Active')


@admin.action(description='Generate fee installments')
def generate_installments(modeladmin, request, queryset):
    """Generate installments for selected enrollments"""
    # This would contain logic to create installments
    pass


# Add actions to respective admin classes
StudentPaymentAdmin.actions = [confirm_payments]
StudentEnrollmentAdmin.actions = [activate_enrollments, generate_installments]