from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from simple_history.models import HistoricalRecords
from decimal import Decimal
from datetime import date, timedelta
import uuid


class FeeCategory(models.Model):
    CATEGORY_TYPES = [
        ('Tuition', 'Tuition Fees'),
        ('Lab', 'Laboratory Fees'),
        ('Technology', 'Technology & Infrastructure'),
        ('Library', 'Library & Resource Fees'),
        ('Transport', 'Transportation'),
        ('Hostel', 'Hostel Accommodation'),
        ('Meal', 'Meal Plans'),
        ('Examination', 'Examination & Certification'),
        ('Sports', 'Sports & Activities'),
        ('Insurance', 'Insurance & Health'),
        ('Miscellaneous', 'Miscellaneous Charges'),
        ('Registration', 'Registration & Admission'),
        ('Security', 'Security Deposit'),
    ]
    
    name = models.CharField(max_length=100, unique=True)
    category_type = models.CharField(max_length=20, choices=CATEGORY_TYPES)
    description = models.TextField(null=True, blank=True)
    
    # Settings
    is_mandatory = models.BooleanField(default=True)
    is_refundable = models.BooleanField(default=False)
    allow_partial_payment = models.BooleanField(default=True)
    
    # Status
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # History tracking
    history = HistoricalRecords()
    
    class Meta:
        ordering = ['category_type', 'name']
        verbose_name_plural = "Fee Categories"
    
    def __str__(self):
        return f"{self.name} ({self.get_category_type_display()})"


class FeeStructure(models.Model):
    name = models.CharField(max_length=200)
    program = models.ForeignKey('academics.Program', on_delete=models.CASCADE, related_name='fee_structures')
    academic_year = models.ForeignKey('academics.AcademicYear', on_delete=models.CASCADE, related_name='fee_structures')
    
    # Validity
    effective_from = models.DateField()
    effective_to = models.DateField(null=True, blank=True)
    
    # Total amounts
    total_fee = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    # Status
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    
    # History tracking
    history = HistoricalRecords()
    
    class Meta:
        ordering = ['-effective_from', 'program']
        unique_together = [['name', 'program', 'academic_year']]
    
    def __str__(self):
        return f"{self.name} - {self.program.name} ({self.academic_year.name})"
    
    def calculate_total_fee(self):
        total = sum(item.amount for item in self.fee_items.filter(is_active=True))
        self.total_fee = total
        self.save(update_fields=['total_fee'])
        return total
    
    def is_current(self):
        today = date.today()
        return (self.effective_from <= today and 
                (not self.effective_to or self.effective_to >= today))


class FeeStructureItem(models.Model):
    fee_structure = models.ForeignKey(FeeStructure, on_delete=models.CASCADE, related_name='fee_items')
    fee_category = models.ForeignKey(FeeCategory, on_delete=models.CASCADE, related_name='structure_items')
    
    # Amount details
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    description = models.CharField(max_length=200, null=True, blank=True)
    
    # Payment settings
    is_mandatory = models.BooleanField(default=True)
    is_one_time = models.BooleanField(default=False, help_text="One-time fee or recurring")
    installment_allowed = models.BooleanField(default=True)
    
    # Due date settings
    due_after_enrollment = models.IntegerField(
        default=30, 
        help_text="Days after enrollment when this fee is due"
    )
    
    # Status
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # History tracking
    history = HistoricalRecords()
    
    class Meta:
        unique_together = [['fee_structure', 'fee_category']]
        ordering = ['fee_category__name']
    
    def __str__(self):
        return f"{self.fee_category.name} - PKR {self.amount}"


class Scholarship(models.Model):
    SCHOLARSHIP_TYPES = [
        ('Merit', 'Merit-based'),
        ('Need', 'Need-based'),
        ('Sports', 'Sports Scholarship'),
        ('Minority', 'Minority Scholarship'),
        ('Employee', 'Employee Child'),
        ('Government', 'Government Scholarship'),
        ('Other', 'Other'),
    ]
    
    DISCOUNT_TYPES = [
        ('Percentage', 'Percentage Discount'),
        ('Fixed', 'Fixed Amount'),
        ('Full', 'Full Waiver'),
    ]
    
    name = models.CharField(max_length=200)
    scholarship_type = models.CharField(max_length=15, choices=SCHOLARSHIP_TYPES)
    description = models.TextField()
    
    # Discount details
    discount_type = models.CharField(max_length=15, choices=DISCOUNT_TYPES)
    discount_percentage = models.DecimalField(
        max_digits=5, decimal_places=2, null=True, blank=True,
        validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    
    # Applicable categories
    applicable_categories = models.ManyToManyField(
        FeeCategory, 
        blank=True,
        help_text="Leave empty for all categories"
    )
    
    # Eligibility criteria
    min_marks = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    max_family_income = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    eligibility_criteria = models.TextField(null=True, blank=True)
    
    # Validity
    valid_from = models.DateField()
    valid_to = models.DateField(null=True, blank=True)
    
    # Limits
    max_recipients = models.IntegerField(null=True, blank=True, help_text="Maximum number of recipients")
    
    # Status
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    
    # History tracking
    history = HistoricalRecords()
    
    class Meta:
        ordering = ['scholarship_type', 'name']
    
    def __str__(self):
        return f"{self.name} ({self.get_scholarship_type_display()})"
    
    def get_discount_display(self):
        if self.discount_type == 'Percentage':
            return f"{self.discount_percentage}%"
        elif self.discount_type == 'Fixed':
            return f"PKR {self.discount_amount}"
        else:
            return "Full Waiver"
    
    def calculate_discount(self, amount):
        if self.discount_type == 'Percentage':
            return amount * (self.discount_percentage / 100)
        elif self.discount_type == 'Fixed':
            return min(self.discount_amount, amount)
        else:  # Full waiver
            return amount
    
    def is_valid(self):
        today = date.today()
        return (self.valid_from <= today and 
                (not self.valid_to or self.valid_to >= today))


class StudentEnrollment(models.Model):
    STATUS_CHOICES = [
        ('Applied', 'Application Submitted'),
        ('Enrolled', 'Enrolled'),
        ('Active', 'Active'),
        ('Completed', 'Completed'),
        ('Withdrawn', 'Withdrawn'),
        ('Suspended', 'Suspended'),
        ('Transferred', 'Transferred'),
    ]
    
    student = models.ForeignKey('students.Student', on_delete=models.CASCADE, related_name='enrollments')
    session = models.ForeignKey('academics.Session', on_delete=models.CASCADE, related_name='enrollments')
    fee_structure = models.ForeignKey(FeeStructure, on_delete=models.PROTECT, related_name='enrollments')
    
    # Enrollment details
    enrollment_date = models.DateField(auto_now_add=True)
    status = models.CharField(max_length=15, choices=STATUS_CHOICES, default='Applied')
    
    # Fee details
    total_fee = models.DecimalField(max_digits=10, decimal_places=2)
    scholarship = models.ForeignKey(Scholarship, on_delete=models.SET_NULL, null=True, blank=True)
    scholarship_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    net_fee = models.DecimalField(max_digits=10, decimal_places=2)
    
    # Payment tracking
    total_paid = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    balance = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    # Dates
    fee_due_date = models.DateField()
    completion_date = models.DateField(null=True, blank=True)
    withdrawal_date = models.DateField(null=True, blank=True)
    
    # System fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    
    # History tracking
    history = HistoricalRecords()
    
    class Meta:
        unique_together = [['student', 'session']]
        ordering = ['-enrollment_date']
    
    def __str__(self):
        return f"{self.student.get_full_name()} - {self.session.name}"
    
    def save(self, *args, **kwargs):
        if not self.total_fee:
            self.total_fee = self.fee_structure.total_fee
        
        # Calculate scholarship discount
        if self.scholarship and not self.scholarship_amount:
            self.scholarship_amount = self.scholarship.calculate_discount(self.total_fee)
        
        # Calculate net fee
        self.net_fee = self.total_fee - self.scholarship_amount
        
        # Calculate balance
        self.balance = self.net_fee - self.total_paid
        
        # Set fee due date if not set
        if not self.fee_due_date:
            self.fee_due_date = self.enrollment_date + timedelta(days=30)
        
        super().save(*args, **kwargs)
    
    def update_payment_totals(self):
        self.total_paid = sum(
            payment.amount for payment in self.payments.filter(status='Confirmed')
        )
        self.balance = self.net_fee - self.total_paid
        self.save(update_fields=['total_paid', 'balance'])
    
    def is_fee_paid(self):
        return self.balance <= 0
    
    def is_overdue(self):
        return self.balance > 0 and date.today() > self.fee_due_date


class StudentPayment(models.Model):
    PAYMENT_METHODS = [
        ('Cash', 'Cash'),
        ('Bank Transfer', 'Bank Transfer'),
        ('Online', 'Online Payment'),
        ('Cheque', 'Cheque'),
        ('Card', 'Debit/Credit Card'),
        ('Mobile Banking', 'Mobile Banking'),
        ('Other', 'Other'),
    ]
    
    STATUS_CHOICES = [
        ('Pending', 'Pending'),
        ('Confirmed', 'Confirmed'),
        ('Failed', 'Failed'),
        ('Cancelled', 'Cancelled'),
        ('Refunded', 'Refunded'),
    ]
    
    # Generate unique payment ID
    payment_id = models.CharField(max_length=20, unique=True, editable=False)
    enrollment = models.ForeignKey(StudentEnrollment, on_delete=models.CASCADE, related_name='payments')
    
    # Payment details
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHODS)
    payment_date = models.DateField()
    
    # Reference details
    reference_number = models.CharField(max_length=100, null=True, blank=True)
    bank_name = models.CharField(max_length=100, null=True, blank=True)
    cheque_number = models.CharField(max_length=50, null=True, blank=True)
    
    # Status
    status = models.CharField(max_length=15, choices=STATUS_CHOICES, default='Pending')
    confirmation_date = models.DateTimeField(null=True, blank=True)
    confirmed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='confirmed_payments')
    
    # Notes
    remarks = models.TextField(null=True, blank=True)
    
    # System fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    
    # History tracking
    history = HistoricalRecords()
    
    class Meta:
        ordering = ['-payment_date', '-created_at']
    
    def __str__(self):
        return f"Payment {self.payment_id} - PKR {self.amount} by {self.enrollment.student.get_full_name()}"
    
    def save(self, *args, **kwargs):
        if not self.payment_id:
            self.payment_id = self.generate_payment_id()
        
        # Update enrollment payment totals when payment is confirmed
        if self.status == 'Confirmed' and self.pk:
            old_status = StudentPayment.objects.get(pk=self.pk).status if self.pk else None
            if old_status != 'Confirmed':
                super().save(*args, **kwargs)
                self.enrollment.update_payment_totals()
                return
        
        super().save(*args, **kwargs)
    
    def generate_payment_id(self):
        year = date.today().year
        count = StudentPayment.objects.filter(payment_id__startswith=f"PAY{year}").count() + 1
        return f"PAY{year}{count:06d}"


class FeeInstallment(models.Model):
    enrollment = models.ForeignKey(StudentEnrollment, on_delete=models.CASCADE, related_name='installments')
    installment_number = models.IntegerField()
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    due_date = models.DateField()
    
    # Status
    is_paid = models.BooleanField(default=False)
    paid_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    paid_date = models.DateField(null=True, blank=True)
    
    # System fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # History tracking
    history = HistoricalRecords()
    
    class Meta:
        unique_together = [['enrollment', 'installment_number']]
        ordering = ['enrollment', 'installment_number']
    
    def __str__(self):
        return f"Installment {self.installment_number} - {self.enrollment.student.get_full_name()}"
    
    def is_overdue(self):
        return not self.is_paid and date.today() > self.due_date
    
    def remaining_amount(self):
        return self.amount - self.paid_amount


class RefundPolicy(models.Model):
    name = models.CharField(max_length=200)
    description = models.TextField()
    
    # Refund rules
    days_after_enrollment = models.IntegerField(help_text="Days after enrollment for refund eligibility")
    refund_percentage = models.DecimalField(max_digits=5, decimal_places=2,
                                          validators=[MinValueValidator(0), MaxValueValidator(100)])
    
    # Applicable to
    programs = models.ManyToManyField('academics.Program', blank=True, help_text="Leave empty for all programs")
    fee_categories = models.ManyToManyField(FeeCategory, blank=True, help_text="Leave empty for all categories")
    
    # Status
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # History tracking
    history = HistoricalRecords()
    
    class Meta:
        ordering = ['days_after_enrollment']
    
    def __str__(self):
        return f"{self.name} - {self.refund_percentage}% refund"


class StudentRefund(models.Model):
    STATUS_CHOICES = [
        ('Requested', 'Refund Requested'),
        ('Approved', 'Approved'),
        ('Rejected', 'Rejected'),
        ('Processed', 'Processed'),
        ('Completed', 'Completed'),
    ]
    
    REFUND_REASONS = [
        ('Withdrawal', 'Course Withdrawal'),
        ('Cancellation', 'Course Cancellation'),
        ('Medical', 'Medical Reasons'),
        ('Transfer', 'Transfer to Another Program'),
        ('Other', 'Other'),
    ]
    
    refund_id = models.CharField(max_length=20, unique=True, editable=False)
    enrollment = models.ForeignKey(StudentEnrollment, on_delete=models.CASCADE, related_name='refunds')
    
    # Refund details
    reason = models.CharField(max_length=15, choices=REFUND_REASONS)
    refund_amount = models.DecimalField(max_digits=10, decimal_places=2)
    requested_amount = models.DecimalField(max_digits=10, decimal_places=2)
    
    # Dates
    request_date = models.DateField(auto_now_add=True)
    approval_date = models.DateField(null=True, blank=True)
    processing_date = models.DateField(null=True, blank=True)
    
    # Status
    status = models.CharField(max_length=15, choices=STATUS_CHOICES, default='Requested')
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                  related_name='approved_refunds')
    processed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='processed_refunds')
    
    # Notes
    request_notes = models.TextField(null=True, blank=True)
    approval_notes = models.TextField(null=True, blank=True)
    processing_notes = models.TextField(null=True, blank=True)
    
    # System fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # History tracking
    history = HistoricalRecords()
    
    class Meta:
        ordering = ['-request_date']
    
    def __str__(self):
        return f"Refund {self.refund_id} - PKR {self.refund_amount}"
    
    def save(self, *args, **kwargs):
        if not self.refund_id:
            self.refund_id = self.generate_refund_id()
        super().save(*args, **kwargs)
    
    def generate_refund_id(self):
        year = date.today().year
        count = StudentRefund.objects.filter(refund_id__startswith=f"REF{year}").count() + 1
        return f"REF{year}{count:06d}"


class StudentTransfer(models.Model):
    TRANSFER_TYPES = [
        ('Class', 'Class Transfer'),
        ('Session', 'Session Transfer'),
        ('Program', 'Program Transfer'),
        ('Campus', 'Campus Transfer'),
        ('Section', 'Section Transfer'),
    ]
    
    STATUS_CHOICES = [
        ('Requested', 'Transfer Requested'),
        ('Approved', 'Approved'),
        ('Rejected', 'Rejected'),
        ('Completed', 'Completed'),
        ('Cancelled', 'Cancelled'),
    ]
    
    transfer_id = models.CharField(max_length=20, unique=True, editable=False)
    student = models.ForeignKey('students.Student', on_delete=models.CASCADE, related_name='transfers')
    
    # Transfer details
    transfer_type = models.CharField(max_length=15, choices=TRANSFER_TYPES)
    
    # From (current)
    from_enrollment = models.ForeignKey(
        StudentEnrollment, 
        on_delete=models.CASCADE, 
        related_name='outgoing_transfers'
    )
    
    # To (new)
    to_session = models.ForeignKey(
        'academics.Session', 
        on_delete=models.CASCADE, 
        related_name='incoming_transfers'
    )
    to_fee_structure = models.ForeignKey(
        FeeStructure, 
        on_delete=models.PROTECT, 
        related_name='transfer_enrollments'
    )
    
    # Dates
    request_date = models.DateField(auto_now_add=True)
    effective_date = models.DateField()
    approval_date = models.DateField(null=True, blank=True)
    completion_date = models.DateField(null=True, blank=True)
    
    # Status
    status = models.CharField(max_length=15, choices=STATUS_CHOICES, default='Requested')
    requested_by = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True,
        related_name='requested_transfers'
    )
    approved_by = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='approved_transfers'
    )
    
    # Fee adjustment
    fee_adjustment = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    adjustment_reason = models.TextField(null=True, blank=True)
    
    # Notes
    request_reason = models.TextField()
    approval_notes = models.TextField(null=True, blank=True)
    rejection_reason = models.TextField(null=True, blank=True)
    
    # System fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # History tracking
    history = HistoricalRecords()
    
    class Meta:
        ordering = ['-request_date']
    
    def __str__(self):
        return f"Transfer {self.transfer_id} - {self.student.get_full_name()}"
    
    def save(self, *args, **kwargs):
        if not self.transfer_id:
            self.transfer_id = self.generate_transfer_id()
        super().save(*args, **kwargs)
    
    def generate_transfer_id(self):
        year = date.today().year
        count = StudentTransfer.objects.filter(transfer_id__startswith=f"TRF{year}").count() + 1
        return f"TRF{year}{count:06d}"
    
    def complete_transfer(self):
        if self.status == 'Approved':
            # Create new enrollment
            new_enrollment = StudentEnrollment.objects.create(
                student=self.student,
                session=self.to_session,
                fee_structure=self.to_fee_structure,
                status='Enrolled',
                total_fee=self.to_fee_structure.total_fee + self.fee_adjustment,
                net_fee=self.to_fee_structure.total_fee + self.fee_adjustment,
                created_by=self.approved_by
            )
            
            # Update old enrollment status
            self.from_enrollment.status = 'Transferred'
            self.from_enrollment.save()
            
            # Update transfer status
            self.status = 'Completed'
            self.completion_date = date.today()
            self.save()
            
            return new_enrollment
        
        return None


class StudentAttendance(models.Model):
    ATTENDANCE_STATUS = [
        ('Present', 'Present'),
        ('Absent', 'Absent'),
        ('Late', 'Late'),
        ('Excused', 'Excused Absence'),
        ('Medical', 'Medical Leave'),
    ]
    
    enrollment = models.ForeignKey(StudentEnrollment, on_delete=models.CASCADE, related_name='attendance')
    session_course = models.ForeignKey(
        'academics.SessionCourse', 
        on_delete=models.CASCADE, 
        related_name='attendance_records',
        null=True, 
        blank=True
    )
    
    # Attendance details
    date = models.DateField()
    status = models.CharField(max_length=10, choices=ATTENDANCE_STATUS)
    arrival_time = models.TimeField(null=True, blank=True)
    remarks = models.TextField(null=True, blank=True)
    
    # System fields
    recorded_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    recorded_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # History tracking
    history = HistoricalRecords()
    
    class Meta:
        unique_together = [['enrollment', 'session_course', 'date']]
        ordering = ['-date', 'enrollment']
        indexes = [
            models.Index(fields=['enrollment', 'date']),
            models.Index(fields=['session_course', 'date']),
        ]
    
    def __str__(self):
        return f"{self.enrollment.student.get_full_name()} - {self.date} - {self.status}"


class StudentGrade(models.Model):
    GRADE_TYPES = [
        ('Assignment', 'Assignment'),
        ('Quiz', 'Quiz'),
        ('Midterm', 'Midterm Exam'),
        ('Final', 'Final Exam'),
        ('Project', 'Project'),
        ('Practical', 'Practical/Lab'),
        ('Participation', 'Class Participation'),
        ('Other', 'Other'),
    ]
    
    enrollment = models.ForeignKey(StudentEnrollment, on_delete=models.CASCADE, related_name='grades')
    session_course = models.ForeignKey(
        'academics.SessionCourse', 
        on_delete=models.CASCADE, 
        related_name='student_grades'
    )
    
    # Grade details
    grade_type = models.CharField(max_length=15, choices=GRADE_TYPES)
    assessment_name = models.CharField(max_length=200)
    marks_obtained = models.DecimalField(max_digits=6, decimal_places=2)
    total_marks = models.DecimalField(max_digits=6, decimal_places=2)
    percentage = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    
    # Grade/Letter
    letter_grade = models.CharField(max_length=5, null=True, blank=True)
    gpa_points = models.DecimalField(max_digits=3, decimal_places=2, null=True, blank=True)
    
    # Dates
    assessment_date = models.DateField()
    
    # System fields
    recorded_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    recorded_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # History tracking
    history = HistoricalRecords()
    
    class Meta:
        unique_together = [['enrollment', 'session_course', 'grade_type', 'assessment_name']]
        ordering = ['-assessment_date', 'enrollment']
    
    def __str__(self):
        return f"{self.enrollment.student.get_full_name()} - {self.assessment_name} - {self.marks_obtained}/{self.total_marks}"
    
    def save(self, *args, **kwargs):
        # Calculate percentage
        if self.marks_obtained and self.total_marks:
            self.percentage = (self.marks_obtained / self.total_marks) * 100
        
        # Auto-assign letter grade based on percentage
        if self.percentage:
            if self.percentage >= 85:
                self.letter_grade = 'A+'
                self.gpa_points = Decimal('4.00')
            elif self.percentage >= 80:
                self.letter_grade = 'A'
                self.gpa_points = Decimal('3.70')
            elif self.percentage >= 75:
                self.letter_grade = 'B+'
                self.gpa_points = Decimal('3.30')
            elif self.percentage >= 70:
                self.letter_grade = 'B'
                self.gpa_points = Decimal('3.00')
            elif self.percentage >= 65:
                self.letter_grade = 'C+'
                self.gpa_points = Decimal('2.70')
            elif self.percentage >= 60:
                self.letter_grade = 'C'
                self.gpa_points = Decimal('2.30')
            elif self.percentage >= 55:
                self.letter_grade = 'D+'
                self.gpa_points = Decimal('2.00')
            elif self.percentage >= 50:
                self.letter_grade = 'D'
                self.gpa_points = Decimal('1.70')
            else:
                self.letter_grade = 'F'
                self.gpa_points = Decimal('0.00')
        
        super().save(*args, **kwargs)
