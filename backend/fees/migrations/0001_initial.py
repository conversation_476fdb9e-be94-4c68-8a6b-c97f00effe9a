# Generated by Django 5.2.4 on 2025-07-25 20:20

import django.core.validators
import django.db.models.deletion
import simple_history.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('academics', '0001_initial'),
        ('students', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='FeeCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('category_type', models.CharField(choices=[('Tuition', 'Tuition Fees'), ('Lab', 'Laboratory Fees'), ('Technology', 'Technology & Infrastructure'), ('Library', 'Library & Resource Fees'), ('Transport', 'Transportation'), ('Hostel', 'Hostel Accommodation'), ('Meal', 'Meal Plans'), ('Examination', 'Examination & Certification'), ('Sports', 'Sports & Activities'), ('Insurance', 'Insurance & Health'), ('Miscellaneous', 'Miscellaneous Charges'), ('Registration', 'Registration & Admission'), ('Security', 'Security Deposit')], max_length=20)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_mandatory', models.BooleanField(default=True)),
                ('is_refundable', models.BooleanField(default=False)),
                ('allow_partial_payment', models.BooleanField(default=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Fee Categories',
                'ordering': ['category_type', 'name'],
            },
        ),
        migrations.CreateModel(
            name='FeeStructure',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('effective_from', models.DateField()),
                ('effective_to', models.DateField(blank=True, null=True)),
                ('total_fee', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('academic_year', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fee_structures', to='academics.academicyear')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fee_structures', to='academics.program')),
            ],
            options={
                'ordering': ['-effective_from', 'program'],
                'unique_together': {('name', 'program', 'academic_year')},
            },
        ),
        migrations.CreateModel(
            name='HistoricalFeeCategory',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('name', models.CharField(db_index=True, max_length=100)),
                ('category_type', models.CharField(choices=[('Tuition', 'Tuition Fees'), ('Lab', 'Laboratory Fees'), ('Technology', 'Technology & Infrastructure'), ('Library', 'Library & Resource Fees'), ('Transport', 'Transportation'), ('Hostel', 'Hostel Accommodation'), ('Meal', 'Meal Plans'), ('Examination', 'Examination & Certification'), ('Sports', 'Sports & Activities'), ('Insurance', 'Insurance & Health'), ('Miscellaneous', 'Miscellaneous Charges'), ('Registration', 'Registration & Admission'), ('Security', 'Security Deposit')], max_length=20)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_mandatory', models.BooleanField(default=True)),
                ('is_refundable', models.BooleanField(default=False)),
                ('allow_partial_payment', models.BooleanField(default=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical fee category',
                'verbose_name_plural': 'historical Fee Categories',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalFeeStructure',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('effective_from', models.DateField()),
                ('effective_to', models.DateField(blank=True, null=True)),
                ('total_fee', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('academic_year', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='academics.academicyear')),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('program', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='academics.program')),
            ],
            options={
                'verbose_name': 'historical fee structure',
                'verbose_name_plural': 'historical fee structures',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalFeeStructureItem',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('description', models.CharField(blank=True, max_length=200, null=True)),
                ('is_mandatory', models.BooleanField(default=True)),
                ('is_one_time', models.BooleanField(default=False, help_text='One-time fee or recurring')),
                ('installment_allowed', models.BooleanField(default=True)),
                ('due_after_enrollment', models.IntegerField(default=30, help_text='Days after enrollment when this fee is due')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('fee_category', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='fees.feecategory')),
                ('fee_structure', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='fees.feestructure')),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical fee structure item',
                'verbose_name_plural': 'historical fee structure items',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalRefundPolicy',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('days_after_enrollment', models.IntegerField(help_text='Days after enrollment for refund eligibility')),
                ('refund_percentage', models.DecimalField(decimal_places=2, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical refund policy',
                'verbose_name_plural': 'historical refund policys',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalScholarship',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('scholarship_type', models.CharField(choices=[('Merit', 'Merit-based'), ('Need', 'Need-based'), ('Sports', 'Sports Scholarship'), ('Minority', 'Minority Scholarship'), ('Employee', 'Employee Child'), ('Government', 'Government Scholarship'), ('Other', 'Other')], max_length=15)),
                ('description', models.TextField()),
                ('discount_type', models.CharField(choices=[('Percentage', 'Percentage Discount'), ('Fixed', 'Fixed Amount'), ('Full', 'Full Waiver')], max_length=15)),
                ('discount_percentage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('discount_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('min_marks', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('max_family_income', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('eligibility_criteria', models.TextField(blank=True, null=True)),
                ('valid_from', models.DateField()),
                ('valid_to', models.DateField(blank=True, null=True)),
                ('max_recipients', models.IntegerField(blank=True, help_text='Maximum number of recipients', null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical scholarship',
                'verbose_name_plural': 'historical scholarships',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='RefundPolicy',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('days_after_enrollment', models.IntegerField(help_text='Days after enrollment for refund eligibility')),
                ('refund_percentage', models.DecimalField(decimal_places=2, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('fee_categories', models.ManyToManyField(blank=True, help_text='Leave empty for all categories', to='fees.feecategory')),
                ('programs', models.ManyToManyField(blank=True, help_text='Leave empty for all programs', to='academics.program')),
            ],
            options={
                'ordering': ['days_after_enrollment'],
            },
        ),
        migrations.CreateModel(
            name='Scholarship',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('scholarship_type', models.CharField(choices=[('Merit', 'Merit-based'), ('Need', 'Need-based'), ('Sports', 'Sports Scholarship'), ('Minority', 'Minority Scholarship'), ('Employee', 'Employee Child'), ('Government', 'Government Scholarship'), ('Other', 'Other')], max_length=15)),
                ('description', models.TextField()),
                ('discount_type', models.CharField(choices=[('Percentage', 'Percentage Discount'), ('Fixed', 'Fixed Amount'), ('Full', 'Full Waiver')], max_length=15)),
                ('discount_percentage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('discount_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('min_marks', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('max_family_income', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('eligibility_criteria', models.TextField(blank=True, null=True)),
                ('valid_from', models.DateField()),
                ('valid_to', models.DateField(blank=True, null=True)),
                ('max_recipients', models.IntegerField(blank=True, help_text='Maximum number of recipients', null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('applicable_categories', models.ManyToManyField(blank=True, help_text='Leave empty for all categories', to='fees.feecategory')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['scholarship_type', 'name'],
            },
        ),
        migrations.CreateModel(
            name='HistoricalStudentEnrollment',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('enrollment_date', models.DateField(blank=True, editable=False)),
                ('status', models.CharField(choices=[('Applied', 'Application Submitted'), ('Enrolled', 'Enrolled'), ('Active', 'Active'), ('Completed', 'Completed'), ('Withdrawn', 'Withdrawn'), ('Suspended', 'Suspended'), ('Transferred', 'Transferred')], default='Applied', max_length=15)),
                ('total_fee', models.DecimalField(decimal_places=2, max_digits=10)),
                ('scholarship_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('net_fee', models.DecimalField(decimal_places=2, max_digits=10)),
                ('total_paid', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('balance', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('fee_due_date', models.DateField()),
                ('completion_date', models.DateField(blank=True, null=True)),
                ('withdrawal_date', models.DateField(blank=True, null=True)),
                ('created_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('fee_structure', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='fees.feestructure')),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('session', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='academics.session')),
                ('student', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='students.student')),
                ('scholarship', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='fees.scholarship')),
            ],
            options={
                'verbose_name': 'historical student enrollment',
                'verbose_name_plural': 'historical student enrollments',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='StudentEnrollment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('enrollment_date', models.DateField(auto_now_add=True)),
                ('status', models.CharField(choices=[('Applied', 'Application Submitted'), ('Enrolled', 'Enrolled'), ('Active', 'Active'), ('Completed', 'Completed'), ('Withdrawn', 'Withdrawn'), ('Suspended', 'Suspended'), ('Transferred', 'Transferred')], default='Applied', max_length=15)),
                ('total_fee', models.DecimalField(decimal_places=2, max_digits=10)),
                ('scholarship_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('net_fee', models.DecimalField(decimal_places=2, max_digits=10)),
                ('total_paid', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('balance', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('fee_due_date', models.DateField()),
                ('completion_date', models.DateField(blank=True, null=True)),
                ('withdrawal_date', models.DateField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('fee_structure', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='enrollments', to='fees.feestructure')),
                ('scholarship', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='fees.scholarship')),
                ('session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='enrollments', to='academics.session')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='enrollments', to='students.student')),
            ],
            options={
                'ordering': ['-enrollment_date'],
                'unique_together': {('student', 'session')},
            },
        ),
        migrations.CreateModel(
            name='HistoricalStudentTransfer',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('transfer_id', models.CharField(db_index=True, editable=False, max_length=20)),
                ('transfer_type', models.CharField(choices=[('Class', 'Class Transfer'), ('Session', 'Session Transfer'), ('Program', 'Program Transfer'), ('Campus', 'Campus Transfer'), ('Section', 'Section Transfer')], max_length=15)),
                ('request_date', models.DateField(blank=True, editable=False)),
                ('effective_date', models.DateField()),
                ('approval_date', models.DateField(blank=True, null=True)),
                ('completion_date', models.DateField(blank=True, null=True)),
                ('status', models.CharField(choices=[('Requested', 'Transfer Requested'), ('Approved', 'Approved'), ('Rejected', 'Rejected'), ('Completed', 'Completed'), ('Cancelled', 'Cancelled')], default='Requested', max_length=15)),
                ('fee_adjustment', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('adjustment_reason', models.TextField(blank=True, null=True)),
                ('request_reason', models.TextField()),
                ('approval_notes', models.TextField(blank=True, null=True)),
                ('rejection_reason', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('approved_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('requested_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('student', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='students.student')),
                ('to_fee_structure', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='fees.feestructure')),
                ('to_session', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='academics.session')),
                ('from_enrollment', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='fees.studentenrollment')),
            ],
            options={
                'verbose_name': 'historical student transfer',
                'verbose_name_plural': 'historical student transfers',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalStudentRefund',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('refund_id', models.CharField(db_index=True, editable=False, max_length=20)),
                ('reason', models.CharField(choices=[('Withdrawal', 'Course Withdrawal'), ('Cancellation', 'Course Cancellation'), ('Medical', 'Medical Reasons'), ('Transfer', 'Transfer to Another Program'), ('Other', 'Other')], max_length=15)),
                ('refund_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('requested_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('request_date', models.DateField(blank=True, editable=False)),
                ('approval_date', models.DateField(blank=True, null=True)),
                ('processing_date', models.DateField(blank=True, null=True)),
                ('status', models.CharField(choices=[('Requested', 'Refund Requested'), ('Approved', 'Approved'), ('Rejected', 'Rejected'), ('Processed', 'Processed'), ('Completed', 'Completed')], default='Requested', max_length=15)),
                ('request_notes', models.TextField(blank=True, null=True)),
                ('approval_notes', models.TextField(blank=True, null=True)),
                ('processing_notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('approved_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('processed_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('enrollment', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='fees.studentenrollment')),
            ],
            options={
                'verbose_name': 'historical student refund',
                'verbose_name_plural': 'historical student refunds',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalStudentPayment',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('payment_id', models.CharField(db_index=True, editable=False, max_length=20)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('payment_method', models.CharField(choices=[('Cash', 'Cash'), ('Bank Transfer', 'Bank Transfer'), ('Online', 'Online Payment'), ('Cheque', 'Cheque'), ('Card', 'Debit/Credit Card'), ('Mobile Banking', 'Mobile Banking'), ('Other', 'Other')], max_length=20)),
                ('payment_date', models.DateField()),
                ('reference_number', models.CharField(blank=True, max_length=100, null=True)),
                ('bank_name', models.CharField(blank=True, max_length=100, null=True)),
                ('cheque_number', models.CharField(blank=True, max_length=50, null=True)),
                ('status', models.CharField(choices=[('Pending', 'Pending'), ('Confirmed', 'Confirmed'), ('Failed', 'Failed'), ('Cancelled', 'Cancelled'), ('Refunded', 'Refunded')], default='Pending', max_length=15)),
                ('confirmation_date', models.DateTimeField(blank=True, null=True)),
                ('remarks', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('confirmed_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('enrollment', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='fees.studentenrollment')),
            ],
            options={
                'verbose_name': 'historical student payment',
                'verbose_name_plural': 'historical student payments',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalStudentGrade',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('grade_type', models.CharField(choices=[('Assignment', 'Assignment'), ('Quiz', 'Quiz'), ('Midterm', 'Midterm Exam'), ('Final', 'Final Exam'), ('Project', 'Project'), ('Practical', 'Practical/Lab'), ('Participation', 'Class Participation'), ('Other', 'Other')], max_length=15)),
                ('assessment_name', models.CharField(max_length=200)),
                ('marks_obtained', models.DecimalField(decimal_places=2, max_digits=6)),
                ('total_marks', models.DecimalField(decimal_places=2, max_digits=6)),
                ('percentage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('letter_grade', models.CharField(blank=True, max_length=5, null=True)),
                ('gpa_points', models.DecimalField(blank=True, decimal_places=2, max_digits=3, null=True)),
                ('assessment_date', models.DateField()),
                ('recorded_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('recorded_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('session_course', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='academics.sessioncourse')),
                ('enrollment', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='fees.studentenrollment')),
            ],
            options={
                'verbose_name': 'historical student grade',
                'verbose_name_plural': 'historical student grades',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalStudentAttendance',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('date', models.DateField()),
                ('status', models.CharField(choices=[('Present', 'Present'), ('Absent', 'Absent'), ('Late', 'Late'), ('Excused', 'Excused Absence'), ('Medical', 'Medical Leave')], max_length=10)),
                ('arrival_time', models.TimeField(blank=True, null=True)),
                ('remarks', models.TextField(blank=True, null=True)),
                ('recorded_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('recorded_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('session_course', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='academics.sessioncourse')),
                ('enrollment', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='fees.studentenrollment')),
            ],
            options={
                'verbose_name': 'historical student attendance',
                'verbose_name_plural': 'historical student attendances',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalFeeInstallment',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('installment_number', models.IntegerField()),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('due_date', models.DateField()),
                ('is_paid', models.BooleanField(default=False)),
                ('paid_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('paid_date', models.DateField(blank=True, null=True)),
                ('created_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('enrollment', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='fees.studentenrollment')),
            ],
            options={
                'verbose_name': 'historical fee installment',
                'verbose_name_plural': 'historical fee installments',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='StudentPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('payment_id', models.CharField(editable=False, max_length=20, unique=True)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('payment_method', models.CharField(choices=[('Cash', 'Cash'), ('Bank Transfer', 'Bank Transfer'), ('Online', 'Online Payment'), ('Cheque', 'Cheque'), ('Card', 'Debit/Credit Card'), ('Mobile Banking', 'Mobile Banking'), ('Other', 'Other')], max_length=20)),
                ('payment_date', models.DateField()),
                ('reference_number', models.CharField(blank=True, max_length=100, null=True)),
                ('bank_name', models.CharField(blank=True, max_length=100, null=True)),
                ('cheque_number', models.CharField(blank=True, max_length=50, null=True)),
                ('status', models.CharField(choices=[('Pending', 'Pending'), ('Confirmed', 'Confirmed'), ('Failed', 'Failed'), ('Cancelled', 'Cancelled'), ('Refunded', 'Refunded')], default='Pending', max_length=15)),
                ('confirmation_date', models.DateTimeField(blank=True, null=True)),
                ('remarks', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('confirmed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='confirmed_payments', to=settings.AUTH_USER_MODEL)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('enrollment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='fees.studentenrollment')),
            ],
            options={
                'ordering': ['-payment_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='StudentRefund',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('refund_id', models.CharField(editable=False, max_length=20, unique=True)),
                ('reason', models.CharField(choices=[('Withdrawal', 'Course Withdrawal'), ('Cancellation', 'Course Cancellation'), ('Medical', 'Medical Reasons'), ('Transfer', 'Transfer to Another Program'), ('Other', 'Other')], max_length=15)),
                ('refund_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('requested_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('request_date', models.DateField(auto_now_add=True)),
                ('approval_date', models.DateField(blank=True, null=True)),
                ('processing_date', models.DateField(blank=True, null=True)),
                ('status', models.CharField(choices=[('Requested', 'Refund Requested'), ('Approved', 'Approved'), ('Rejected', 'Rejected'), ('Processed', 'Processed'), ('Completed', 'Completed')], default='Requested', max_length=15)),
                ('request_notes', models.TextField(blank=True, null=True)),
                ('approval_notes', models.TextField(blank=True, null=True)),
                ('processing_notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_refunds', to=settings.AUTH_USER_MODEL)),
                ('enrollment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='refunds', to='fees.studentenrollment')),
                ('processed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='processed_refunds', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-request_date'],
            },
        ),
        migrations.CreateModel(
            name='StudentTransfer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transfer_id', models.CharField(editable=False, max_length=20, unique=True)),
                ('transfer_type', models.CharField(choices=[('Class', 'Class Transfer'), ('Session', 'Session Transfer'), ('Program', 'Program Transfer'), ('Campus', 'Campus Transfer'), ('Section', 'Section Transfer')], max_length=15)),
                ('request_date', models.DateField(auto_now_add=True)),
                ('effective_date', models.DateField()),
                ('approval_date', models.DateField(blank=True, null=True)),
                ('completion_date', models.DateField(blank=True, null=True)),
                ('status', models.CharField(choices=[('Requested', 'Transfer Requested'), ('Approved', 'Approved'), ('Rejected', 'Rejected'), ('Completed', 'Completed'), ('Cancelled', 'Cancelled')], default='Requested', max_length=15)),
                ('fee_adjustment', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('adjustment_reason', models.TextField(blank=True, null=True)),
                ('request_reason', models.TextField()),
                ('approval_notes', models.TextField(blank=True, null=True)),
                ('rejection_reason', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_transfers', to=settings.AUTH_USER_MODEL)),
                ('from_enrollment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='outgoing_transfers', to='fees.studentenrollment')),
                ('requested_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='requested_transfers', to=settings.AUTH_USER_MODEL)),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transfers', to='students.student')),
                ('to_fee_structure', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='transfer_enrollments', to='fees.feestructure')),
                ('to_session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='incoming_transfers', to='academics.session')),
            ],
            options={
                'ordering': ['-request_date'],
            },
        ),
        migrations.CreateModel(
            name='FeeStructureItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('description', models.CharField(blank=True, max_length=200, null=True)),
                ('is_mandatory', models.BooleanField(default=True)),
                ('is_one_time', models.BooleanField(default=False, help_text='One-time fee or recurring')),
                ('installment_allowed', models.BooleanField(default=True)),
                ('due_after_enrollment', models.IntegerField(default=30, help_text='Days after enrollment when this fee is due')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('fee_category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='structure_items', to='fees.feecategory')),
                ('fee_structure', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fee_items', to='fees.feestructure')),
            ],
            options={
                'ordering': ['fee_category__name'],
                'unique_together': {('fee_structure', 'fee_category')},
            },
        ),
        migrations.CreateModel(
            name='StudentAttendance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('status', models.CharField(choices=[('Present', 'Present'), ('Absent', 'Absent'), ('Late', 'Late'), ('Excused', 'Excused Absence'), ('Medical', 'Medical Leave')], max_length=10)),
                ('arrival_time', models.TimeField(blank=True, null=True)),
                ('remarks', models.TextField(blank=True, null=True)),
                ('recorded_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('recorded_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('session_course', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='attendance_records', to='academics.sessioncourse')),
                ('enrollment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendance', to='fees.studentenrollment')),
            ],
            options={
                'ordering': ['-date', 'enrollment'],
                'indexes': [models.Index(fields=['enrollment', 'date'], name='fees_studen_enrollm_09847b_idx'), models.Index(fields=['session_course', 'date'], name='fees_studen_session_2ac074_idx')],
                'unique_together': {('enrollment', 'session_course', 'date')},
            },
        ),
        migrations.CreateModel(
            name='FeeInstallment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('installment_number', models.IntegerField()),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('due_date', models.DateField()),
                ('is_paid', models.BooleanField(default=False)),
                ('paid_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('paid_date', models.DateField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('enrollment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='installments', to='fees.studentenrollment')),
            ],
            options={
                'ordering': ['enrollment', 'installment_number'],
                'unique_together': {('enrollment', 'installment_number')},
            },
        ),
        migrations.CreateModel(
            name='StudentGrade',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('grade_type', models.CharField(choices=[('Assignment', 'Assignment'), ('Quiz', 'Quiz'), ('Midterm', 'Midterm Exam'), ('Final', 'Final Exam'), ('Project', 'Project'), ('Practical', 'Practical/Lab'), ('Participation', 'Class Participation'), ('Other', 'Other')], max_length=15)),
                ('assessment_name', models.CharField(max_length=200)),
                ('marks_obtained', models.DecimalField(decimal_places=2, max_digits=6)),
                ('total_marks', models.DecimalField(decimal_places=2, max_digits=6)),
                ('percentage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('letter_grade', models.CharField(blank=True, max_length=5, null=True)),
                ('gpa_points', models.DecimalField(blank=True, decimal_places=2, max_digits=3, null=True)),
                ('assessment_date', models.DateField()),
                ('recorded_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('enrollment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='grades', to='fees.studentenrollment')),
                ('recorded_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('session_course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='student_grades', to='academics.sessioncourse')),
            ],
            options={
                'ordering': ['-assessment_date', 'enrollment'],
                'unique_together': {('enrollment', 'session_course', 'grade_type', 'assessment_name')},
            },
        ),
    ]
