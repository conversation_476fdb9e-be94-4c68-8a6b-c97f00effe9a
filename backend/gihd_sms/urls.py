"""
URL configuration for gihd_sms project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from rest_framework_simplejwt.views import (
    TokenObtainPairView,
    TokenRefreshView,
)

# Customize admin site header
admin.site.site_header = "GIHD Student Management System"
admin.site.site_title = "GIHD SMS Admin"
admin.site.index_title = "Welcome to GIHD SMS Administration"

urlpatterns = [
    path('admin/', admin.site.urls),
    
    # Authentication API
    path('api/auth/', include('authentication.urls')),
    
    # JWT Authentication (legacy)
    path('api/token/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('api/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    
    # App APIs
    path('api/students/', include('students.urls')),
    path('api/academics/', include('academics.urls')),
    path('api/fees/', include('fees.urls')),
    path('api/documents/', include('documents.urls')),
    path('api/reports/', include('reports.urls')),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
