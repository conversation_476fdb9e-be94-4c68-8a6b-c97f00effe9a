"""
Common utilities for GIHD School Management System

This app provides standardized response formats, exception handling,
and common mixins for consistent API behavior across all apps.
"""

# Delay imports to avoid circular dependencies during Django startup
def get_components():
    from .response import (
        StandardResponse,
        success_response,
        error_response,
        created_response,
        updated_response,
        deleted_response,
        not_found_response,
        validation_error_response
    )

    from .exceptions import (
        APIException,
        ValidationException,
        NotFound,
        Unauthorized,
        Forbidden,
        ServerError,
        custom_exception_handler
    )

    from .mixins import (
        StandardResponseMixin,
        ValidationMixin,
        PermissionMixin,
        BulkOperationMixin
    )

    from .decorators import (
        standard_response,
        handle_exceptions,
        validate_data,
        permission_required,
        log_api_call
    )

    from .pagination import (
        StandardPageNumberPagination,
        LargeResultsSetPagination,
        SmallResultsSetPagination
    )
    
    return {
        'StandardResponse': StandardResponse,
        'success_response': success_response,
        'error_response': error_response,
        'created_response': created_response,
        'updated_response': updated_response,
        'deleted_response': deleted_response,
        'not_found_response': not_found_response,
        'validation_error_response': validation_error_response,
        'APIException': APIException,
        'ValidationException': ValidationException,
        'NotFound': NotFound,
        'Unauthorized': Unauthorized,
        'Forbidden': Forbidden,
        'ServerError': ServerError,
        'custom_exception_handler': custom_exception_handler,
        'StandardResponseMixin': StandardResponseMixin,
        'ValidationMixin': ValidationMixin,
        'PermissionMixin': PermissionMixin,
        'BulkOperationMixin': BulkOperationMixin,
        'standard_response': standard_response,
        'handle_exceptions': handle_exceptions,
        'validate_data': validate_data,
        'permission_required': permission_required,
        'log_api_call': log_api_call,
        'StandardPageNumberPagination': StandardPageNumberPagination,
        'LargeResultsSetPagination': LargeResultsSetPagination,
        'SmallResultsSetPagination': SmallResultsSetPagination,
    }

__all__ = [
    # Response utilities
    'StandardResponse',
    'success_response',
    'error_response', 
    'created_response',
    'updated_response',
    'deleted_response',
    'not_found_response',
    'validation_error_response',
    
    # Exception handling
    'APIException',
    'ValidationException',
    'NotFound',
    'Unauthorized',
    'Forbidden',
    'ServerError',
    'custom_exception_handler',
    
    # Mixins
    'StandardResponseMixin',
    'ValidationMixin',
    'PermissionMixin',
    'BulkOperationMixin',
    
    # Decorators
    'standard_response',
    'handle_exceptions',
    'validate_data',
    'permission_required',
    'log_api_call',
    
    # Pagination
    'StandardPageNumberPagination',
    'LargeResultsSetPagination',
    'SmallResultsSetPagination',
]