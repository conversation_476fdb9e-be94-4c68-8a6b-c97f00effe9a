"""
Example usage of the common app utilities

This file demonstrates how to use the standardized response format,
mixins, decorators, and exception handling in your views.
"""

from rest_framework import generics, viewsets, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from django.contrib.auth.models import User

# Import common utilities
from common import (
    StandardResponse,
    StandardResponseMixin,
    ValidationMixin,
    BulkOperationMixin,
    standard_response,
    handle_exceptions,
    validate_data,
    APIException,
    ValidationException,
    NotFound
)

# Example serializer (you would import your actual serializers)
from rest_framework import serializers

class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name']


# Example 1: Using StandardResponse directly in function-based view
@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def user_list_create(request):
    """Example function-based view using StandardResponse"""
    
    if request.method == 'GET':
        users = User.objects.all()
        serializer = UserSerializer(users, many=True)
        
        return StandardResponse.success(
            data=serializer.data,
            message="Users retrieved successfully"
        )
    
    elif request.method == 'POST':
        serializer = UserSerializer(data=request.data)
        
        if not serializer.is_valid():
            return StandardResponse.validation_error(
                errors=serializer.errors,
                message="Validation failed"
            )
        
        user = serializer.save()
        
        return StandardResponse.created(
            data=serializer.data,
            message="User created successfully"
        )


# Example 2: Using decorator for automatic response wrapping
@api_view(['GET'])
@permission_classes([IsAuthenticated])
@standard_response("User profile retrieved successfully")
def get_user_profile(request):
    """Example using decorator for automatic response wrapping"""
    
    # Just return the data - decorator handles the standard response format
    serializer = UserSerializer(request.user)
    return serializer.data


# Example 3: Using decorator with validation
@api_view(['POST'])
@permission_classes([IsAuthenticated])
@validate_data(required_fields=['username', 'email'])
@handle_exceptions
def create_user_validated(request):
    """Example using validation decorator"""
    
    # Validation is already done by decorator
    # request.validated_data contains cleaned data if schema was used
    
    serializer = UserSerializer(data=request.data)
    if not serializer.is_valid():
        raise ValidationException(serializer.errors)
    
    user = serializer.save()
    
    return StandardResponse.created(
        data=serializer.data,
        message="User created successfully"
    )


# Example 4: Using mixins in class-based views
class UserViewSet(StandardResponseMixin, ValidationMixin, viewsets.ModelViewSet):
    """
    Example ViewSet using common mixins
    
    StandardResponseMixin automatically formats all CRUD responses
    ValidationMixin provides validation utilities
    """
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [IsAuthenticated]
    
    def create(self, request, *args, **kwargs):
        """Override create to add custom logic while keeping standard response"""
        
        # Use validation mixin
        serializer = self.get_serializer(data=request.data)
        validation_error = self.validate_serializer(serializer)
        if validation_error:
            return validation_error
        
        # Custom business logic here
        if User.objects.filter(username=serializer.validated_data['username']).exists():
            return StandardResponse.error(
                message="Username already exists",
                http_code=status.HTTP_400_BAD_REQUEST
            )
        
        # Let the mixin handle the response formatting
        return super().create(request, *args, **kwargs)


# Example 5: Using bulk operations mixin
class BulkUserViewSet(BulkOperationMixin, StandardResponseMixin, viewsets.ModelViewSet):
    """Example ViewSet with bulk operations"""
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [IsAuthenticated]
    
    @api_view(['POST'])
    def bulk_create_users(self, request):
        """Create multiple users at once"""
        return self.bulk_create(request)
    
    @api_view(['PUT'])
    def bulk_update_users(self, request):
        """Update multiple users at once"""
        return self.bulk_update(request)
    
    @api_view(['DELETE'])
    def bulk_delete_users(self, request):
        """Delete multiple users at once"""
        return self.bulk_delete(request)


# Example 6: Custom exception usage
@api_view(['GET'])
@permission_classes([IsAuthenticated])
@handle_exceptions
def get_user_by_id(request, user_id):
    """Example using custom exceptions"""
    
    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        raise NotFound(f"User with ID {user_id} not found")
    
    if not request.user.is_staff and user != request.user:
        raise APIException(
            message="You can only view your own profile",
            http_code=status.HTTP_403_FORBIDDEN
        )
    
    serializer = UserSerializer(user)
    return StandardResponse.success(
        data=serializer.data,
        message="User retrieved successfully"
    )


# Example 7: Mixed approach - combining different utilities
class UserProfileView(StandardResponseMixin, ValidationMixin, generics.RetrieveUpdateAPIView):
    """
    Example view combining multiple common utilities
    """
    serializer_class = UserSerializer
    permission_classes = [IsAuthenticated]
    
    def get_object(self):
        """Always return the current user's profile"""
        return self.request.user
    
    def update(self, request, *args, **kwargs):
        """Custom update with additional validation"""
        
        # Use validation mixin
        serializer = self.get_serializer(self.get_object(), data=request.data, partial=True)
        validation_error = self.validate_serializer(serializer)
        if validation_error:
            return validation_error
        
        # Custom validation
        if 'username' in request.data:
            if User.objects.filter(
                username=request.data['username']
            ).exclude(id=request.user.id).exists():
                return StandardResponse.validation_error(
                    errors={'username': ['This username is already taken']},
                    message="Username validation failed"
                )
        
        # Let the mixin handle the standard response
        return super().update(request, *args, **kwargs)


# Example API response formats that these examples would produce:

"""
Success Response:
{
    "status": "success",
    "http_code": 200,
    "message": "Users retrieved successfully",
    "data": [
        {
            "id": 1,
            "username": "john_doe",
            "email": "<EMAIL>",
            "first_name": "John",
            "last_name": "Doe"
        }
    ]
}

Error Response:
{
    "status": "error",
    "http_code": 400,
    "message": "Validation failed",
    "errors": {
        "username": ["This field is required"],
        "email": ["Enter a valid email address"]
    }
}

Created Response:
{
    "status": "success",
    "http_code": 201,
    "message": "User created successfully",
    "data": {
        "id": 2,
        "username": "jane_doe",
        "email": "<EMAIL>",
        "first_name": "Jane",
        "last_name": "Doe"
    }
}

Paginated Response:
{
    "status": "success",
    "http_code": 200,
    "message": "Data retrieved successfully",
    "data": {
        "results": [...],
        "pagination": {
            "count": 100,
            "page": 1,
            "page_size": 20,
            "total_pages": 5,
            "has_next": true,
            "has_previous": false,
            "next_page": 2,
            "previous_page": null,
            "links": {
                "next": "http://example.com/api/users/?page=2",
                "previous": null
            }
        }
    }
}
"""