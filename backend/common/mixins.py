"""
Common mixins for API views to ensure standardized responses
"""
from rest_framework import status
from rest_framework.response import Response
from .response import StandardResponse


class StandardResponseMixin:
    """
    Mixin to provide standardized responses for common CRUD operations
    """
    
    def create(self, request, *args, **kwargs):
        """Override create to return standardized response"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        
        return StandardResponse.created(
            data=serializer.data,
            message=f"{self.get_model_name()} created successfully"
        )
    
    def update(self, request, *args, **kwargs):
        """Override update to return standardized response"""
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        
        return StandardResponse.updated(
            data=serializer.data,
            message=f"{self.get_model_name()} updated successfully"
        )
    
    def destroy(self, request, *args, **kwargs):
        """Override destroy to return standardized response"""
        instance = self.get_object()
        self.perform_destroy(instance)
        
        return StandardResponse.deleted(
            message=f"{self.get_model_name()} deleted successfully"
        )
    
    def list(self, request, *args, **kwargs):
        """Override list to return standardized response"""
        queryset = self.filter_queryset(self.get_queryset())
        
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            paginated_response = self.get_paginated_response(serializer.data)
            
            # Convert paginated response to standard format
            return StandardResponse.success(
                data=paginated_response.data,
                message=f"{self.get_model_name()} list retrieved successfully"
            )
        
        serializer = self.get_serializer(queryset, many=True)
        return StandardResponse.success(
            data=serializer.data,
            message=f"{self.get_model_name()} list retrieved successfully"
        )
    
    def retrieve(self, request, *args, **kwargs):
        """Override retrieve to return standardized response"""
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        
        return StandardResponse.success(
            data=serializer.data,
            message=f"{self.get_model_name()} retrieved successfully"
        )
    
    def get_model_name(self):
        """Get the model name for response messages"""
        if hasattr(self, 'queryset') and self.queryset is not None:
            return self.queryset.model._meta.verbose_name.title()
        elif hasattr(self, 'serializer_class') and self.serializer_class is not None:
            model = getattr(self.serializer_class.Meta, 'model', None)
            if model:
                return model._meta.verbose_name.title()
        return "Resource"


class ValidationMixin:
    """
    Mixin to handle validation with standardized error responses
    """
    
    def validate_serializer(self, serializer):
        """
        Validate serializer data and return standardized error if invalid
        
        Args:
            serializer: DRF serializer instance
            
        Returns:
            Response: Standardized validation error response if invalid, None if valid
        """
        if not serializer.is_valid():
            return StandardResponse.validation_error(
                errors=serializer.errors,
                message="Validation failed"
            )
        return None


class PermissionMixin:
    """
    Mixin to handle permission checks with standardized error responses
    """
    
    def check_permissions(self, request):
        """Override to return standardized permission denied response"""
        try:
            super().check_permissions(request)
        except Exception as exc:
            return StandardResponse.forbidden(message="Permission denied")
    
    def check_object_permissions(self, request, obj):
        """Override to return standardized permission denied response"""
        try:
            super().check_object_permissions(request, obj)
        except Exception as exc:
            return StandardResponse.forbidden(message="Permission denied for this object")


class BulkOperationMixin:
    """
    Mixin to handle bulk operations with standardized responses
    """
    
    def bulk_create(self, request, *args, **kwargs):
        """Handle bulk creation of objects"""
        if not isinstance(request.data, list):
            return StandardResponse.error(
                message="Expected a list of objects for bulk creation",
                http_code=status.HTTP_400_BAD_REQUEST
            )
        
        serializer = self.get_serializer(data=request.data, many=True)
        if not serializer.is_valid():
            return StandardResponse.validation_error(
                errors=serializer.errors,
                message="Validation failed for bulk creation"
            )
        
        instances = serializer.save()
        
        return StandardResponse.created(
            data=serializer.data,
            message=f"{len(instances)} {self.get_model_name()}s created successfully"
        )
    
    def bulk_update(self, request, *args, **kwargs):
        """Handle bulk update of objects"""
        if not isinstance(request.data, list):
            return StandardResponse.error(
                message="Expected a list of objects for bulk update",
                http_code=status.HTTP_400_BAD_REQUEST
            )
        
        updated_count = 0
        errors = []
        
        for item_data in request.data:
            if 'id' not in item_data:
                errors.append("Each object must have an 'id' field for bulk update")
                continue
            
            try:
                instance = self.get_queryset().get(id=item_data['id'])
                serializer = self.get_serializer(instance, data=item_data, partial=True)
                
                if serializer.is_valid():
                    serializer.save()
                    updated_count += 1
                else:
                    errors.extend([f"Object {item_data['id']}: {error}" for error in serializer.errors])
            except self.get_queryset().model.DoesNotExist:
                errors.append(f"Object with id {item_data['id']} not found")
        
        if errors:
            return StandardResponse.error(
                message="Bulk update completed with errors",
                errors=errors,
                data={"updated_count": updated_count}
            )
        
        return StandardResponse.updated(
            data={"updated_count": updated_count},
            message=f"{updated_count} {self.get_model_name()}s updated successfully"
        )
    
    def bulk_delete(self, request, *args, **kwargs):
        """Handle bulk deletion of objects"""
        ids = request.data.get('ids', [])
        
        if not ids or not isinstance(ids, list):
            return StandardResponse.error(
                message="Expected a list of IDs for bulk deletion",
                http_code=status.HTTP_400_BAD_REQUEST
            )
        
        queryset = self.get_queryset().filter(id__in=ids)
        deleted_count = queryset.count()
        
        if deleted_count == 0:
            return StandardResponse.not_found(message="No objects found with the provided IDs")
        
        queryset.delete()
        
        return StandardResponse.deleted(
            message=f"{deleted_count} {self.get_model_name()}s deleted successfully"
        )