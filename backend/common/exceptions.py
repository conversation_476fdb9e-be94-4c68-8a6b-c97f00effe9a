"""
Custom exception handlers for standardized error responses
"""
from rest_framework.views import exception_handler
from rest_framework import status
from django.http import Http404
from django.core.exceptions import PermissionDenied, ValidationError
from .response import StandardResponse
import logging

logger = logging.getLogger(__name__)


def custom_exception_handler(exc, context):
    """
    Custom exception handler that returns standardized error responses
    
    Args:
        exc: The exception instance
        context: The context in which the exception occurred
        
    Returns:
        Response: Standardized error response
    """
    # Call REST framework's default exception handler first,
    # to get the standard error response.
    response = exception_handler(exc, context)
    
    if response is not None:
        # Convert DRF error response to our standard format
        if hasattr(exc, 'detail'):
            if isinstance(exc.detail, dict):
                # Validation errors
                return StandardResponse.validation_error(
                    errors=exc.detail,
                    message="Validation failed"
                )
            elif isinstance(exc.detail, list):
                # List of errors
                return StandardResponse.error(
                    message=str(exc.detail[0]) if exc.detail else "An error occurred",
                    http_code=response.status_code
                )
            else:
                # String error message
                return StandardResponse.error(
                    message=str(exc.detail),
                    http_code=response.status_code
                )
        else:
            return StandardResponse.error(
                message="An error occurred",
                http_code=response.status_code
            )
    
    # Handle Django exceptions that DRF doesn't catch
    if isinstance(exc, Http404):
        return StandardResponse.not_found(message="Resource not found")
    
    if isinstance(exc, PermissionDenied):
        return StandardResponse.forbidden(message="Permission denied")
    
    if isinstance(exc, ValidationError):
        if hasattr(exc, 'message_dict'):
            return StandardResponse.validation_error(
                errors=exc.message_dict,
                message="Validation failed"
            )
        else:
            return StandardResponse.error(
                message=str(exc),
                http_code=status.HTTP_400_BAD_REQUEST
            )
    
    # For unhandled exceptions, log them and return a generic error
    logger.exception(f"Unhandled exception: {exc}")
    return StandardResponse.server_error(message="An unexpected error occurred")


class APIException(Exception):
    """
    Base API exception class for custom exceptions
    """
    def __init__(self, message="An error occurred", http_code=status.HTTP_400_BAD_REQUEST, errors=None):
        self.message = message
        self.http_code = http_code
        self.errors = errors
        super().__init__(self.message)


class ValidationException(APIException):
    """Exception for validation errors"""
    def __init__(self, errors, message="Validation failed"):
        super().__init__(message=message, http_code=status.HTTP_400_BAD_REQUEST, errors=errors)


class NotFound(APIException):
    """Exception for not found errors"""
    def __init__(self, message="Resource not found"):
        super().__init__(message=message, http_code=status.HTTP_404_NOT_FOUND)


class Unauthorized(APIException):
    """Exception for unauthorized access"""
    def __init__(self, message="Authentication required"):
        super().__init__(message=message, http_code=status.HTTP_401_UNAUTHORIZED)


class Forbidden(APIException):
    """Exception for forbidden access"""
    def __init__(self, message="Permission denied"):
        super().__init__(message=message, http_code=status.HTTP_403_FORBIDDEN)


class ServerError(APIException):
    """Exception for server errors"""
    def __init__(self, message="Internal server error"):
        super().__init__(message=message, http_code=status.HTTP_500_INTERNAL_SERVER_ERROR)