"""
Decorators for API views to ensure standardized responses
"""
from functools import wraps
from rest_framework import status
from .response import StandardResponse
from .exceptions import APIException
import logging

logger = logging.getLogger(__name__)


def standard_response(success_message="Operation completed successfully"):
    """
    Decorator to automatically wrap view responses in standard format
    
    Args:
        success_message: Default success message
        
    Usage:
        @standard_response("User logged in successfully")
        def login_view(request):
            # Your view logic here
            return {"user": user_data, "token": token}
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(*args, **kwargs):
            try:
                result = view_func(*args, **kwargs)
                
                # If result is already a Response object, return as-is
                if hasattr(result, 'status_code'):
                    return result
                
                # If result is a dict or other data, wrap in standard response
                return StandardResponse.success(
                    data=result,
                    message=success_message
                )
                
            except APIException as e:
                return StandardResponse.error(
                    message=e.message,
                    errors=e.errors,
                    http_code=e.http_code
                )
            except Exception as e:
                logger.exception(f"Unexpected error in {view_func.__name__}: {e}")
                return StandardResponse.server_error(
                    message="An unexpected error occurred"
                )
        
        return wrapper
    return decorator


def handle_exceptions(view_func):
    """
    Decorator to catch and handle exceptions with standardized responses
    
    Usage:
        @handle_exceptions
        def my_view(request):
            # Your view logic here
            pass
    """
    @wraps(view_func)
    def wrapper(*args, **kwargs):
        try:
            return view_func(*args, **kwargs)
        except APIException as e:
            return StandardResponse.error(
                message=e.message,
                errors=e.errors,
                http_code=e.http_code
            )
        except Exception as e:
            logger.exception(f"Unexpected error in {view_func.__name__}: {e}")
            return StandardResponse.server_error(
                message="An unexpected error occurred"
            )
    
    return wrapper


def validate_data(schema=None, required_fields=None):
    """
    Decorator to validate request data
    
    Args:
        schema: Serializer class for validation
        required_fields: List of required field names
        
    Usage:
        @validate_data(required_fields=['name', 'email'])
        def create_user(request):
            # request.data is guaranteed to have name and email
            pass
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # Check required fields
            if required_fields:
                missing_fields = []
                for field in required_fields:
                    if field not in request.data:
                        missing_fields.append(field)
                
                if missing_fields:
                    return StandardResponse.validation_error(
                        errors={"missing_fields": missing_fields},
                        message=f"Missing required fields: {', '.join(missing_fields)}"
                    )
            
            # Validate with schema if provided
            if schema:
                serializer = schema(data=request.data)
                if not serializer.is_valid():
                    return StandardResponse.validation_error(
                        errors=serializer.errors,
                        message="Validation failed"
                    )
                # Add validated data to request
                request.validated_data = serializer.validated_data
            
            return view_func(request, *args, **kwargs)
        
        return wrapper
    return decorator


def permission_required(permission_check):
    """
    Decorator to check permissions with standardized error responses
    
    Args:
        permission_check: Function that takes request and returns bool
        
    Usage:
        @permission_required(lambda request: request.user.is_staff)
        def admin_only_view(request):
            pass
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            if not permission_check(request):
                return StandardResponse.forbidden(
                    message="You don't have permission to access this resource"
                )
            
            return view_func(request, *args, **kwargs)
        
        return wrapper
    return decorator


def rate_limit(max_requests=100, window_minutes=60):
    """
    Simple rate limiting decorator (placeholder implementation)
    
    Args:
        max_requests: Maximum requests allowed in time window
        window_minutes: Time window in minutes
        
    Note: This is a placeholder. In production, use Redis or proper rate limiting.
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # Placeholder implementation
            # In production, implement proper rate limiting with Redis
            return view_func(request, *args, **kwargs)
        
        return wrapper
    return decorator


def log_api_call(log_request=True, log_response=False):
    """
    Decorator to log API calls
    
    Args:
        log_request: Whether to log request data
        log_response: Whether to log response data
        
    Usage:
        @log_api_call(log_request=True, log_response=True)
        def my_api_view(request):
            pass
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            if log_request:
                logger.info(f"API Call: {view_func.__name__} - User: {getattr(request.user, 'username', 'Anonymous')}")
                if hasattr(request, 'data'):
                    logger.debug(f"Request data: {request.data}")
            
            response = view_func(request, *args, **kwargs)
            
            if log_response:
                logger.debug(f"Response: {response.data if hasattr(response, 'data') else response}")
            
            return response
        
        return wrapper
    return decorator