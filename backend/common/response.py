"""
Common response utilities for standardized API responses
"""
from rest_framework.response import Response
from rest_framework import status
from typing import Any, Dict, Optional, Union
import logging

logger = logging.getLogger(__name__)


class StandardResponse:
    """
    Standardized API response format with consistent structure:
    {
        "status": "success"|"error",
        "http_code": 200,
        "message": "Operation completed successfully",
        "data": {...}
    }
    """
    
    @staticmethod
    def success(
        data: Any = None,
        message: str = "Operation completed successfully",
        http_code: int = status.HTTP_200_OK
    ) -> Response:
        """
        Create a successful response
        
        Args:
            data: Response data (can be dict, list, string, etc.)
            message: Success message
            http_code: HTTP status code
            
        Returns:
            Response: DRF Response object with standardized format
        """
        response_data = {
            "status": "success",
            "http_code": http_code,
            "message": message,
            "data": data
        }
        
        return Response(response_data, status=http_code)
    
    @staticmethod
    def error(
        message: str = "An error occurred",
        errors: Optional[Dict] = None,
        http_code: int = status.HTTP_400_BAD_REQUEST,
        data: Any = None
    ) -> Response:
        """
        Create an error response
        
        Args:
            message: Error message
            errors: Detailed error information (e.g., validation errors)
            http_code: HTTP status code
            data: Additional data (optional)
            
        Returns:
            Response: DRF Response object with standardized format
        """
        response_data = {
            "status": "error",
            "http_code": http_code,
            "message": message,
        }
        
        if errors:
            response_data["errors"] = errors
            
        if data is not None:
            response_data["data"] = data
            
        # Log error for debugging
        logger.error(f"API Error: {message} - Code: {http_code}")
        if errors:
            logger.error(f"Error details: {errors}")
            
        return Response(response_data, status=http_code)
    
    @staticmethod
    def created(
        data: Any = None,
        message: str = "Resource created successfully",
        http_code: int = status.HTTP_201_CREATED
    ) -> Response:
        """
        Create a successful creation response
        
        Args:
            data: Created resource data
            message: Success message
            http_code: HTTP status code (default 201)
            
        Returns:
            Response: DRF Response object with standardized format
        """
        return StandardResponse.success(data=data, message=message, http_code=http_code)
    
    @staticmethod
    def updated(
        data: Any = None,
        message: str = "Resource updated successfully",
        http_code: int = status.HTTP_200_OK
    ) -> Response:
        """
        Create a successful update response
        
        Args:
            data: Updated resource data
            message: Success message
            http_code: HTTP status code
            
        Returns:
            Response: DRF Response object with standardized format
        """
        return StandardResponse.success(data=data, message=message, http_code=http_code)
    
    @staticmethod
    def deleted(
        message: str = "Resource deleted successfully",
        http_code: int = status.HTTP_200_OK
    ) -> Response:
        """
        Create a successful deletion response
        
        Args:
            message: Success message
            http_code: HTTP status code
            
        Returns:
            Response: DRF Response object with standardized format
        """
        return StandardResponse.success(message=message, http_code=http_code)
    
    @staticmethod
    def not_found(
        message: str = "Resource not found",
        http_code: int = status.HTTP_404_NOT_FOUND
    ) -> Response:
        """
        Create a not found error response
        
        Args:
            message: Error message
            http_code: HTTP status code
            
        Returns:
            Response: DRF Response object with standardized format
        """
        return StandardResponse.error(message=message, http_code=http_code)
    
    @staticmethod
    def unauthorized(
        message: str = "Authentication required",
        http_code: int = status.HTTP_401_UNAUTHORIZED
    ) -> Response:
        """
        Create an unauthorized error response
        
        Args:
            message: Error message
            http_code: HTTP status code
            
        Returns:
            Response: DRF Response object with standardized format
        """
        return StandardResponse.error(message=message, http_code=http_code)
    
    @staticmethod
    def forbidden(
        message: str = "Permission denied",
        http_code: int = status.HTTP_403_FORBIDDEN
    ) -> Response:
        """
        Create a forbidden error response
        
        Args:
            message: Error message
            http_code: HTTP status code
            
        Returns:
            Response: DRF Response object with standardized format
        """
        return StandardResponse.error(message=message, http_code=http_code)
    
    @staticmethod 
    def validation_error(
        errors: Dict,
        message: str = "Validation failed",
        http_code: int = status.HTTP_400_BAD_REQUEST
    ) -> Response:
        """
        Create a validation error response
        
        Args:
            errors: Validation error details
            message: Error message
            http_code: HTTP status code
            
        Returns:
            Response: DRF Response object with standardized format
        """
        return StandardResponse.error(message=message, errors=errors, http_code=http_code)
    
    @staticmethod
    def server_error(
        message: str = "Internal server error",
        http_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR
    ) -> Response:
        """
        Create a server error response
        
        Args:
            message: Error message
            http_code: HTTP status code
            
        Returns:
            Response: DRF Response object with standardized format
        """
        return StandardResponse.error(message=message, http_code=http_code)


# Convenience response functions for direct import
def success_response(data=None, message="Operation completed successfully", http_code=status.HTTP_200_OK):
    """Shorthand for StandardResponse.success()"""
    return StandardResponse.success(data=data, message=message, http_code=http_code)


def error_response(message="An error occurred", errors=None, http_code=status.HTTP_400_BAD_REQUEST, data=None):
    """Shorthand for StandardResponse.error()"""
    return StandardResponse.error(message=message, errors=errors, http_code=http_code, data=data)


def created_response(data=None, message="Resource created successfully"):
    """Shorthand for StandardResponse.created()"""
    return StandardResponse.created(data=data, message=message)


def updated_response(data=None, message="Resource updated successfully"):
    """Shorthand for StandardResponse.updated()"""
    return StandardResponse.updated(data=data, message=message)


def deleted_response(message="Resource deleted successfully"):
    """Shorthand for StandardResponse.deleted()"""
    return StandardResponse.deleted(message=message)


def not_found_response(message="Resource not found"):
    """Shorthand for StandardResponse.not_found()"""
    return StandardResponse.not_found(message=message)


def validation_error_response(errors, message="Validation failed"):
    """Shorthand for StandardResponse.validation_error()"""
    return StandardResponse.validation_error(errors=errors, message=message)