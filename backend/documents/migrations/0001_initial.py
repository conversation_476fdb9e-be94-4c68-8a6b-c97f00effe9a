# Generated by Django 5.2.4 on 2025-07-25 20:20

import datetime
import django.db.models.deletion
import documents.models
import simple_history.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('students', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DocumentCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('category_type', models.CharField(choices=[('Identity', 'Identity Documents'), ('Academic', 'Academic Records'), ('Medical', 'Medical Records'), ('Financial', 'Financial Documents'), ('Legal', 'Legal Documents'), ('Photo', 'Photo Documentation'), ('Other', 'Other Documents')], max_length=15)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_required', models.BooleanField(default=False, help_text='Is this category required for admission?')),
                ('has_expiry', models.BooleanField(default=False, help_text='Do documents in this category have expiry dates?')),
                ('reminder_days', models.IntegerField(default=30, help_text='Days before expiry to send reminder (if applicable)')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Document Categories',
                'ordering': ['category_type', 'name'],
            },
        ),
        migrations.CreateModel(
            name='DocumentType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('max_file_size_mb', models.IntegerField(default=5, help_text='Maximum file size in MB')),
                ('allowed_extensions', models.CharField(default='pdf,jpg,jpeg,png,doc,docx', help_text='Comma-separated list of allowed file extensions', max_length=200)),
                ('is_required', models.BooleanField(default=False)),
                ('has_expiry', models.BooleanField(default=False)),
                ('is_cnic_required', models.BooleanField(default=False, help_text='Is CNIC verification required?')),
                ('is_original_required', models.BooleanField(default=False, help_text='Is original document required?')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='document_types', to='documents.documentcategory')),
            ],
            options={
                'ordering': ['category', 'name'],
                'unique_together': {('name', 'category')},
            },
        ),
        migrations.CreateModel(
            name='HistoricalDocumentCategory',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('name', models.CharField(db_index=True, max_length=100)),
                ('category_type', models.CharField(choices=[('Identity', 'Identity Documents'), ('Academic', 'Academic Records'), ('Medical', 'Medical Records'), ('Financial', 'Financial Documents'), ('Legal', 'Legal Documents'), ('Photo', 'Photo Documentation'), ('Other', 'Other Documents')], max_length=15)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_required', models.BooleanField(default=False, help_text='Is this category required for admission?')),
                ('has_expiry', models.BooleanField(default=False, help_text='Do documents in this category have expiry dates?')),
                ('reminder_days', models.IntegerField(default=30, help_text='Days before expiry to send reminder (if applicable)')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical document category',
                'verbose_name_plural': 'historical Document Categories',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalDocumentType',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('max_file_size_mb', models.IntegerField(default=5, help_text='Maximum file size in MB')),
                ('allowed_extensions', models.CharField(default='pdf,jpg,jpeg,png,doc,docx', help_text='Comma-separated list of allowed file extensions', max_length=200)),
                ('is_required', models.BooleanField(default=False)),
                ('has_expiry', models.BooleanField(default=False)),
                ('is_cnic_required', models.BooleanField(default=False, help_text='Is CNIC verification required?')),
                ('is_original_required', models.BooleanField(default=False, help_text='Is original document required?')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('category', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='documents.documentcategory')),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical document type',
                'verbose_name_plural': 'historical document types',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalStudentDocument',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('file', models.TextField(max_length=100)),
                ('original_filename', models.CharField(max_length=255)),
                ('file_size', models.IntegerField(help_text='File size in bytes')),
                ('document_number', models.CharField(blank=True, help_text='Document ID/Number if applicable', max_length=100, null=True)),
                ('issue_date', models.DateField(blank=True, null=True)),
                ('expiry_date', models.DateField(blank=True, null=True)),
                ('issuing_authority', models.CharField(blank=True, max_length=200, null=True)),
                ('status', models.CharField(choices=[('Pending', 'Pending Review'), ('Verified', 'Verified'), ('Rejected', 'Rejected'), ('Expired', 'Expired'), ('Renewal', 'Renewal Required')], default='Pending', max_length=10)),
                ('verified_at', models.DateTimeField(blank=True, null=True)),
                ('verification_notes', models.TextField(blank=True, null=True)),
                ('original_submitted', models.BooleanField(default=False)),
                ('original_returned', models.BooleanField(default=False)),
                ('original_return_date', models.DateField(blank=True, null=True)),
                ('original_received_by', models.CharField(blank=True, max_length=100, null=True)),
                ('uploaded_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('document_type', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='documents.documenttype')),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('student', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='students.student')),
                ('uploaded_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('verified_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical student document',
                'verbose_name_plural': 'historical student documents',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='StudentDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to=documents.models.get_document_upload_path)),
                ('original_filename', models.CharField(max_length=255)),
                ('file_size', models.IntegerField(help_text='File size in bytes')),
                ('document_number', models.CharField(blank=True, help_text='Document ID/Number if applicable', max_length=100, null=True)),
                ('issue_date', models.DateField(blank=True, null=True)),
                ('expiry_date', models.DateField(blank=True, null=True)),
                ('issuing_authority', models.CharField(blank=True, max_length=200, null=True)),
                ('status', models.CharField(choices=[('Pending', 'Pending Review'), ('Verified', 'Verified'), ('Rejected', 'Rejected'), ('Expired', 'Expired'), ('Renewal', 'Renewal Required')], default='Pending', max_length=10)),
                ('verified_at', models.DateTimeField(blank=True, null=True)),
                ('verification_notes', models.TextField(blank=True, null=True)),
                ('original_submitted', models.BooleanField(default=False)),
                ('original_returned', models.BooleanField(default=False)),
                ('original_return_date', models.DateField(blank=True, null=True)),
                ('original_received_by', models.CharField(blank=True, max_length=100, null=True)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('document_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='student_documents', to='documents.documenttype')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='students.student')),
                ('uploaded_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='uploaded_documents', to=settings.AUTH_USER_MODEL)),
                ('verified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='verified_documents', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-uploaded_at'],
            },
        ),
        migrations.CreateModel(
            name='HistoricalDocumentReminder',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('reminder_type', models.CharField(choices=[('Expiry', 'Document Expiry'), ('Missing', 'Missing Document'), ('Renewal', 'Document Renewal'), ('Verification', 'Verification Required')], max_length=15)),
                ('priority', models.CharField(choices=[('Low', 'Low'), ('Medium', 'Medium'), ('High', 'High'), ('Urgent', 'Urgent')], default='Medium', max_length=10)),
                ('message', models.TextField()),
                ('due_date', models.DateField(blank=True, null=True)),
                ('reminder_date', models.DateField(default=datetime.date.today)),
                ('is_sent', models.BooleanField(default=False)),
                ('sent_at', models.DateTimeField(blank=True, null=True)),
                ('is_resolved', models.BooleanField(default=False)),
                ('resolved_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('document_type', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='documents.documenttype')),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('student', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='students.student')),
                ('document', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='documents.studentdocument')),
            ],
            options={
                'verbose_name': 'historical document reminder',
                'verbose_name_plural': 'historical document reminders',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalDocumentAuditLog',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('action', models.CharField(choices=[('Upload', 'Document Uploaded'), ('Update', 'Document Updated'), ('Verify', 'Document Verified'), ('Reject', 'Document Rejected'), ('Delete', 'Document Deleted'), ('Download', 'Document Downloaded'), ('View', 'Document Viewed')], max_length=10)),
                ('old_status', models.CharField(blank=True, max_length=10, null=True)),
                ('new_status', models.CharField(blank=True, max_length=10, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('timestamp', models.DateTimeField(blank=True, editable=False)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('user', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('document', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='documents.studentdocument')),
            ],
            options={
                'verbose_name': 'historical document audit log',
                'verbose_name_plural': 'historical document audit logs',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='DocumentReminder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reminder_type', models.CharField(choices=[('Expiry', 'Document Expiry'), ('Missing', 'Missing Document'), ('Renewal', 'Document Renewal'), ('Verification', 'Verification Required')], max_length=15)),
                ('priority', models.CharField(choices=[('Low', 'Low'), ('Medium', 'Medium'), ('High', 'High'), ('Urgent', 'Urgent')], default='Medium', max_length=10)),
                ('message', models.TextField()),
                ('due_date', models.DateField(blank=True, null=True)),
                ('reminder_date', models.DateField(default=datetime.date.today)),
                ('is_sent', models.BooleanField(default=False)),
                ('sent_at', models.DateTimeField(blank=True, null=True)),
                ('is_resolved', models.BooleanField(default=False)),
                ('resolved_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='document_reminders', to='students.student')),
                ('document_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reminders', to='documents.documenttype')),
                ('document', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='reminders', to='documents.studentdocument')),
            ],
            options={
                'ordering': ['-priority', 'due_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DocumentAuditLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('Upload', 'Document Uploaded'), ('Update', 'Document Updated'), ('Verify', 'Document Verified'), ('Reject', 'Document Rejected'), ('Delete', 'Document Deleted'), ('Download', 'Document Downloaded'), ('View', 'Document Viewed')], max_length=10)),
                ('old_status', models.CharField(blank=True, max_length=10, null=True)),
                ('new_status', models.CharField(blank=True, max_length=10, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='audit_logs', to='documents.studentdocument')),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.AddIndex(
            model_name='studentdocument',
            index=models.Index(fields=['student', 'document_type'], name='documents_s_student_9ee276_idx'),
        ),
        migrations.AddIndex(
            model_name='studentdocument',
            index=models.Index(fields=['status'], name='documents_s_status_3bee8b_idx'),
        ),
        migrations.AddIndex(
            model_name='studentdocument',
            index=models.Index(fields=['expiry_date'], name='documents_s_expiry__f8fa3d_idx'),
        ),
        migrations.AddIndex(
            model_name='documentreminder',
            index=models.Index(fields=['student', 'is_resolved'], name='documents_d_student_9393af_idx'),
        ),
        migrations.AddIndex(
            model_name='documentreminder',
            index=models.Index(fields=['reminder_date', 'is_sent'], name='documents_d_reminde_ab02bc_idx'),
        ),
        migrations.AddIndex(
            model_name='documentreminder',
            index=models.Index(fields=['due_date'], name='documents_d_due_dat_f16143_idx'),
        ),
    ]
