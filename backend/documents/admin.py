from django.contrib import admin
from django.urls import reverse
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from django.db import models
from django.forms import TextInput, Textarea
from simple_history.admin import SimpleHistoryAdmin
from .models import (
    DocumentCategory, DocumentType, StudentDocument, 
    DocumentReminder, DocumentAuditLog
)


@admin.register(DocumentCategory)
class DocumentCategoryAdmin(SimpleHistoryAdmin):
    list_display = ['name', 'category_type', 'is_required', 'has_expiry', 'reminder_days', 
                   'document_types_count', 'is_active']
    list_filter = ['category_type', 'is_required', 'has_expiry', 'is_active', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['category_type', 'name']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'category_type', 'description')
        }),
        ('Requirements', {
            'fields': ('is_required', 'has_expiry', 'reminder_days')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def document_types_count(self, obj):
        """Display count of document types in this category"""
        count = obj.document_types.filter(is_active=True).count()
        if count > 0:
            url = reverse('admin:documents_documenttype_changelist') + f'?category__id={obj.id}'
            return format_html('<a href="{}">{} Types</a>', url, count)
        return "0 Types"
    document_types_count.short_description = "Document Types"


@admin.register(DocumentType)
class DocumentTypeAdmin(SimpleHistoryAdmin):
    list_display = ['name', 'category', 'max_file_size_mb', 'allowed_extensions_display', 
                   'is_required', 'has_expiry', 'is_cnic_required', 'is_active']
    list_filter = ['category', 'is_required', 'has_expiry', 'is_cnic_required', 
                  'is_original_required', 'is_active', 'created_at']
    search_fields = ['name', 'description', 'allowed_extensions']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['category', 'name']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'category', 'description')
        }),
        ('File Validation', {
            'fields': ('max_file_size_mb', 'allowed_extensions')
        }),
        ('Requirements', {
            'fields': ('is_required', 'has_expiry', 'is_cnic_required', 'is_original_required')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    formfield_overrides = {
        models.CharField: {'widget': TextInput(attrs={'size': '80'})},
    }
    
    def allowed_extensions_display(self, obj):
        """Display allowed extensions in a formatted way"""
        extensions = obj.get_allowed_extensions_list()
        if len(extensions) > 3:
            return f"{', '.join(extensions[:3])}... (+{len(extensions)-3} more)"
        return ', '.join(extensions)
    allowed_extensions_display.short_description = "Allowed Extensions"


@admin.register(StudentDocument)
class StudentDocumentAdmin(SimpleHistoryAdmin):
    list_display = ['student_name', 'document_type', 'status', 'file_size_display', 
                   'expiry_status', 'verification_status', 'uploaded_at']
    list_filter = ['status', 'document_type__category', 'document_type', 'original_submitted', 
                  'original_returned', 'uploaded_at', 'verified_at']
    search_fields = ['student__first_name', 'student__last_name', 'student__student_id', 
                    'document_number', 'issuing_authority']
    readonly_fields = ['file_size', 'original_filename', 'uploaded_at', 'updated_at']
    ordering = ['-uploaded_at']
    date_hierarchy = 'uploaded_at'
    
    fieldsets = (
        ('Student & Document', {
            'fields': ('student', 'document_type')
        }),
        ('File Information', {
            'fields': ('file', 'original_filename', 'file_size')
        }),
        ('Document Details', {
            'fields': ('document_number', 'issue_date', 'expiry_date', 'issuing_authority')
        }),
        ('Verification', {
            'fields': ('status', 'verified_by', 'verified_at', 'verification_notes'),
            'classes': ('collapse',)
        }),
        ('Original Document Tracking', {
            'fields': ('original_submitted', 'original_returned', 'original_return_date', 
                      'original_received_by'),
            'classes': ('collapse',)
        }),
        ('System Information', {
            'fields': ('uploaded_by', 'uploaded_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def student_name(self, obj):
        """Display student name with link"""
        if obj.student:
            url = reverse('admin:students_student_change', args=[obj.student.pk])
            return format_html('<a href="{}">{}</a>', url, obj.student.get_full_name())
        return "-"
    student_name.short_description = "Student"
    student_name.admin_order_field = 'student__first_name'
    
    def file_size_display(self, obj):
        """Display file size in human readable format"""
        return f"{obj.get_file_size_mb()} MB"
    file_size_display.short_description = "File Size"
    file_size_display.admin_order_field = 'file_size'
    
    def expiry_status(self, obj):
        """Display expiry status with color coding"""
        if not obj.expiry_date:
            return "No Expiry"
        
        if obj.is_expired():
            return format_html('<span style="color: red; font-weight: bold;">Expired</span>')
        elif obj.is_expiring_soon():
            days = obj.days_until_expiry()
            return format_html('<span style="color: orange;">Expires in {} days</span>', days)
        else:
            days = obj.days_until_expiry()
            return format_html('<span style="color: green;">Valid ({} days left)</span>', days)
    expiry_status.short_description = "Expiry Status"
    
    def verification_status(self, obj):
        """Display verification status with color coding"""
        colors = {
            'Pending': 'orange',
            'Verified': 'green',
            'Rejected': 'red',
            'Expired': 'gray',
            'Renewal': 'blue'
        }
        color = colors.get(obj.status, 'black')
        return format_html('<span style="color: {};">{}</span>', color, obj.get_status_display())
    verification_status.short_description = "Verification"
    
    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.uploaded_by = request.user
        if obj.status == 'Verified' and not obj.verified_by:
            obj.verified_by = request.user
            from django.utils import timezone
            obj.verified_at = timezone.now()
        super().save_model(request, obj, form, change)


@admin.register(DocumentReminder)
class DocumentReminderAdmin(SimpleHistoryAdmin):
    list_display = ['student_name', 'document_type', 'reminder_type', 'priority', 
                   'due_date', 'is_sent', 'is_resolved', 'created_at']
    list_filter = ['reminder_type', 'priority', 'is_sent', 'is_resolved', 
                  'document_type__category', 'created_at']
    search_fields = ['student__first_name', 'student__last_name', 'student__student_id', 'message']
    readonly_fields = ['created_at', 'updated_at', 'sent_at', 'resolved_at']
    ordering = ['priority', 'due_date', '-created_at']
    date_hierarchy = 'due_date'
    
    fieldsets = (
        ('Reminder Details', {
            'fields': ('student', 'document', 'document_type', 'reminder_type', 'priority')
        }),
        ('Message', {
            'fields': ('message',)
        }),
        ('Dates', {
            'fields': ('due_date', 'reminder_date')
        }),
        ('Status', {
            'fields': ('is_sent', 'sent_at', 'is_resolved', 'resolved_at')
        }),
        ('System Information', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def student_name(self, obj):
        """Display student name with link"""
        if obj.student:
            url = reverse('admin:students_student_change', args=[obj.student.pk])
            return format_html('<a href="{}">{}</a>', url, obj.student.get_full_name())
        return "-"
    student_name.short_description = "Student"
    student_name.admin_order_field = 'student__first_name'
    
    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(DocumentAuditLog)
class DocumentAuditLogAdmin(SimpleHistoryAdmin):
    list_display = ['document_name', 'action', 'user', 'old_status', 'new_status', 'timestamp']
    list_filter = ['action', 'old_status', 'new_status', 'timestamp', 'document__document_type']
    search_fields = ['document__student__first_name', 'document__student__last_name', 
                    'document__student__student_id', 'notes', 'user__username']
    readonly_fields = ['timestamp', 'ip_address']
    ordering = ['-timestamp']
    date_hierarchy = 'timestamp'
    
    fieldsets = (
        ('Audit Information', {
            'fields': ('document', 'action', 'user')
        }),
        ('Status Changes', {
            'fields': ('old_status', 'new_status', 'notes')
        }),
        ('System Information', {
            'fields': ('timestamp', 'ip_address'),
            'classes': ('collapse',)
        })
    )
    
    def document_name(self, obj):
        """Display document name with student info"""
        if obj.document:
            return f"{obj.document.document_type.name} - {obj.document.student.get_full_name()}"
        return "-"
    document_name.short_description = "Document"
    document_name.admin_order_field = 'document__document_type__name'
    
    def has_add_permission(self, request):
        """Audit logs should not be manually added"""
        return False
    
    def has_change_permission(self, request, obj=None):
        """Audit logs should not be edited"""
        return False
    
    def has_delete_permission(self, request, obj=None):
        """Audit logs should not be deleted"""
        return False


# Custom admin actions
@admin.action(description='Mark selected documents as verified')
def mark_as_verified(modeladmin, request, queryset):
    """Mark selected documents as verified"""
    from django.utils import timezone
    queryset.update(
        status='Verified',
        verified_by=request.user,
        verified_at=timezone.now()
    )


@admin.action(description='Mark selected documents as rejected')
def mark_as_rejected(modeladmin, request, queryset):
    """Mark selected documents as rejected"""
    from django.utils import timezone
    queryset.update(
        status='Rejected',
        verified_by=request.user,
        verified_at=timezone.now()
    )


@admin.action(description='Mark selected reminders as resolved')
def mark_reminders_resolved(modeladmin, request, queryset):
    """Mark selected reminders as resolved"""
    from django.utils import timezone
    queryset.update(
        is_resolved=True,
        resolved_at=timezone.now()
    )


# Add actions to respective admin classes
StudentDocumentAdmin.actions = [mark_as_verified, mark_as_rejected]
DocumentReminderAdmin.actions = [mark_reminders_resolved]