from django.db import models
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from simple_history.models import HistoricalRecords
from datetime import date, timedelta
import os


class DocumentCategory(models.Model):
    CATEGORY_TYPES = [
        ('Identity', 'Identity Documents'),
        ('Academic', 'Academic Records'),
        ('Medical', 'Medical Records'),
        ('Financial', 'Financial Documents'),
        ('Legal', 'Legal Documents'),
        ('Photo', 'Photo Documentation'),
        ('Other', 'Other Documents'),
    ]
    
    name = models.CharField(max_length=100, unique=True)
    category_type = models.CharField(max_length=15, choices=CATEGORY_TYPES)
    description = models.TextField(null=True, blank=True)
    is_required = models.BooleanField(default=False, help_text="Is this category required for admission?")
    has_expiry = models.BooleanField(default=False, help_text="Do documents in this category have expiry dates?")
    reminder_days = models.IntegerField(
        default=30, 
        help_text="Days before expiry to send reminder (if applicable)"
    )
    
    # Status
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # History tracking
    history = HistoricalRecords()
    
    class Meta:
        ordering = ['category_type', 'name']
        verbose_name_plural = "Document Categories"
    
    def __str__(self):
        return f"{self.name} ({self.get_category_type_display()})"


class DocumentType(models.Model):
    name = models.CharField(max_length=100)
    category = models.ForeignKey(DocumentCategory, on_delete=models.CASCADE, related_name='document_types')
    description = models.TextField(null=True, blank=True)
    
    # Validation rules
    max_file_size_mb = models.IntegerField(default=5, help_text="Maximum file size in MB")
    allowed_extensions = models.CharField(
        max_length=200, 
        default="pdf,jpg,jpeg,png,doc,docx",
        help_text="Comma-separated list of allowed file extensions"
    )
    
    # Requirements
    is_required = models.BooleanField(default=False)
    has_expiry = models.BooleanField(default=False)
    
    # Pakistani specific document types
    is_cnic_required = models.BooleanField(default=False, help_text="Is CNIC verification required?")
    is_original_required = models.BooleanField(default=False, help_text="Is original document required?")
    
    # Status
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # History tracking
    history = HistoricalRecords()
    
    class Meta:
        ordering = ['category', 'name']
        unique_together = [['name', 'category']]
    
    def __str__(self):
        return f"{self.name} - {self.category.name}"
    
    def get_allowed_extensions_list(self):
        return [ext.strip().lower() for ext in self.allowed_extensions.split(',')]


def get_document_upload_path(instance, filename):
    student_id = instance.student.student_id
    category = instance.document_type.category.name.lower().replace(' ', '_')
    return f'documents/{student_id}/{category}/{filename}'


class StudentDocument(models.Model):
    STATUS_CHOICES = [
        ('Pending', 'Pending Review'),
        ('Verified', 'Verified'),
        ('Rejected', 'Rejected'),
        ('Expired', 'Expired'),
        ('Renewal', 'Renewal Required'),
    ]
    
    student = models.ForeignKey('students.Student', on_delete=models.CASCADE, related_name='documents')
    document_type = models.ForeignKey(DocumentType, on_delete=models.CASCADE, related_name='student_documents')
    
    # File information
    file = models.FileField(upload_to=get_document_upload_path)
    original_filename = models.CharField(max_length=255)
    file_size = models.IntegerField(help_text="File size in bytes")
    
    # Document details
    document_number = models.CharField(max_length=100, null=True, blank=True, 
                                     help_text="Document ID/Number if applicable")
    issue_date = models.DateField(null=True, blank=True)
    expiry_date = models.DateField(null=True, blank=True)
    issuing_authority = models.CharField(max_length=200, null=True, blank=True)
    
    # Verification
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='Pending')
    verified_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                  related_name='verified_documents')
    verified_at = models.DateTimeField(null=True, blank=True)
    verification_notes = models.TextField(null=True, blank=True)
    
    # Original document status (for Pakistani context)
    original_submitted = models.BooleanField(default=False)
    original_returned = models.BooleanField(default=False)
    original_return_date = models.DateField(null=True, blank=True)
    original_received_by = models.CharField(max_length=100, null=True, blank=True)
    
    # System fields
    uploaded_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    uploaded_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, 
                                  related_name='uploaded_documents')
    
    # History tracking
    history = HistoricalRecords()
    
    class Meta:
        ordering = ['-uploaded_at']
        indexes = [
            models.Index(fields=['student', 'document_type']),
            models.Index(fields=['status']),
            models.Index(fields=['expiry_date']),
        ]
    
    def __str__(self):
        return f"{self.document_type.name} - {self.student.get_full_name()}"
    
    def clean(self):
        if self.file:
            # Check file size
            max_size = self.document_type.max_file_size_mb * 1024 * 1024  # Convert to bytes
            if self.file.size > max_size:
                raise ValidationError(f"File size exceeds maximum allowed size of {self.document_type.max_file_size_mb}MB")
            
            # Check file extension
            ext = os.path.splitext(self.file.name)[1][1:].lower()  # Get extension without dot
            allowed_extensions = self.document_type.get_allowed_extensions_list()
            if ext not in allowed_extensions:
                raise ValidationError(f"File extension '{ext}' not allowed. Allowed: {', '.join(allowed_extensions)}")
        
        # Check expiry date logic
        if self.document_type.has_expiry and not self.expiry_date:
            raise ValidationError("Expiry date is required for this document type")
        
        if self.expiry_date and self.issue_date and self.expiry_date <= self.issue_date:
            raise ValidationError("Expiry date must be after issue date")
    
    def save(self, *args, **kwargs):
        if self.file:
            self.original_filename = self.file.name
            self.file_size = self.file.size
        
        # Auto-update status based on expiry
        if self.expiry_date and self.expiry_date < date.today() and self.status != 'Expired':
            self.status = 'Expired'
        
        self.full_clean()
        super().save(*args, **kwargs)
    
    def is_expired(self):
        if not self.expiry_date:
            return False
        return self.expiry_date < date.today()
    
    def is_expiring_soon(self):
        if not self.expiry_date:
            return False
        reminder_days = self.document_type.category.reminder_days
        warning_date = date.today() + timedelta(days=reminder_days)
        return self.expiry_date <= warning_date
    
    def days_until_expiry(self):
        if not self.expiry_date:
            return None
        return (self.expiry_date - date.today()).days
    
    def get_file_extension(self):
        return os.path.splitext(self.original_filename)[1][1:].lower() if self.original_filename else ''
    
    def get_file_size_mb(self):
        return round(self.file_size / (1024 * 1024), 2) if self.file_size else 0


class DocumentReminder(models.Model):
    REMINDER_TYPES = [
        ('Expiry', 'Document Expiry'),
        ('Missing', 'Missing Document'),
        ('Renewal', 'Document Renewal'),
        ('Verification', 'Verification Required'),
    ]
    
    PRIORITY_CHOICES = [
        ('Low', 'Low'),
        ('Medium', 'Medium'),
        ('High', 'High'),
        ('Urgent', 'Urgent'),
    ]
    
    student = models.ForeignKey('students.Student', on_delete=models.CASCADE, related_name='document_reminders')
    document = models.ForeignKey(StudentDocument, on_delete=models.CASCADE, null=True, blank=True,
                               related_name='reminders')
    document_type = models.ForeignKey(DocumentType, on_delete=models.CASCADE, related_name='reminders')
    
    # Reminder details
    reminder_type = models.CharField(max_length=15, choices=REMINDER_TYPES)
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='Medium')
    message = models.TextField()
    
    # Dates
    due_date = models.DateField(null=True, blank=True)
    reminder_date = models.DateField(default=date.today)
    
    # Status
    is_sent = models.BooleanField(default=False)
    sent_at = models.DateTimeField(null=True, blank=True)
    is_resolved = models.BooleanField(default=False)
    resolved_at = models.DateTimeField(null=True, blank=True)
    
    # System fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    
    # History tracking
    history = HistoricalRecords()
    
    class Meta:
        ordering = ['-priority', 'due_date', '-created_at']
        indexes = [
            models.Index(fields=['student', 'is_resolved']),
            models.Index(fields=['reminder_date', 'is_sent']),
            models.Index(fields=['due_date']),
        ]
    
    def __str__(self):
        return f"{self.get_reminder_type_display()} - {self.student.get_full_name()}"


class DocumentAuditLog(models.Model):
    ACTION_CHOICES = [
        ('Upload', 'Document Uploaded'),
        ('Update', 'Document Updated'),
        ('Verify', 'Document Verified'),
        ('Reject', 'Document Rejected'),
        ('Delete', 'Document Deleted'),
        ('Download', 'Document Downloaded'),
        ('View', 'Document Viewed'),
    ]
    
    document = models.ForeignKey(StudentDocument, on_delete=models.CASCADE, related_name='audit_logs')
    action = models.CharField(max_length=10, choices=ACTION_CHOICES)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    
    # Action details
    old_status = models.CharField(max_length=10, null=True, blank=True)
    new_status = models.CharField(max_length=10, null=True, blank=True)
    notes = models.TextField(null=True, blank=True)
    
    # System fields
    timestamp = models.DateTimeField(auto_now_add=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    
    # History tracking
    history = HistoricalRecords()
    
    class Meta:
        ordering = ['-timestamp']
    
    def __str__(self):
        return f"{self.get_action_display()} - {self.document.document_type.name} by {self.user}"
