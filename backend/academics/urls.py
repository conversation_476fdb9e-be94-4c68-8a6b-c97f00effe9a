from django.urls import path
from . import views

urlpatterns = [
    # Academic Year endpoints
    path('academic-years/', views.AcademicYearListCreateView.as_view(), name='academic_year_list_create'),
    path('academic-years/<int:pk>/', views.AcademicYearDetailView.as_view(), name='academic_year_detail'),
    
    # Department endpoints
    path('departments/', views.DepartmentListCreateView.as_view(), name='department_list_create'),
    path('departments/<int:pk>/', views.DepartmentDetailView.as_view(), name='department_detail'),
    
    # Program endpoints
    path('programs/', views.ProgramListCreateView.as_view(), name='program_list_create'),
    path('programs/<int:pk>/', views.ProgramDetailView.as_view(), name='program_detail'),
    
    # Session endpoints
    path('sessions/', views.SessionListCreateView.as_view(), name='session_list_create'),
    path('sessions/<int:pk>/', views.SessionDetailView.as_view(), name='session_detail'),
    
    # Session Course endpoints
    path('sessions/<int:session_id>/courses/', views.SessionCourseListCreateView.as_view(), name='session_course_list_create'),
    path('sessions/<int:session_id>/courses/<int:pk>/', views.SessionCourseDetailView.as_view(), name='session_course_detail'),
    
    # Course endpoints
    path('courses/', views.CourseListCreateView.as_view(), name='course_list_create'),
    path('courses/<int:pk>/', views.CourseDetailView.as_view(), name='course_detail'),
    
    # Campus endpoints
    path('campuses/', views.CampusListCreateView.as_view(), name='campus_list_create'),
    path('campuses/<int:pk>/', views.CampusDetailView.as_view(), name='campus_detail'),
    
    # Classroom endpoints
    path('classrooms/', views.ClassroomListCreateView.as_view(), name='classroom_list_create'),
    path('classrooms/<int:pk>/', views.ClassroomDetailView.as_view(), name='classroom_detail'),
    
    # Statistics endpoint
    path('stats/', views.AcademicsStatsView.as_view(), name='academics_stats'),
]
