from django.contrib import admin
from django.urls import reverse
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from simple_history.admin import SimpleHistoryAdmin
from .models import (
    AcademicYear, Department, Program, Session, Course, 
    SessionCourse, Campus, Classroom
)


@admin.register(AcademicYear)
class AcademicYearAdmin(SimpleHistoryAdmin):
    list_display = ['name', 'start_date', 'end_date', 'is_active', 'duration_days', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['-start_date']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'start_date', 'end_date', 'is_active')
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def duration_days(self, obj):
        """Display the duration of the academic year in days"""
        if obj.start_date and obj.end_date:
            duration = (obj.end_date - obj.start_date).days
            return f"{duration} days"
        return "-"
    duration_days.short_description = "Duration"
    
    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(Department)
class DepartmentAdmin(SimpleHistoryAdmin):
    list_display = ['name', 'code', 'head_of_department', 'is_active', 'programs_count', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'code', 'description']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['name']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'code', 'description')
        }),
        ('Management', {
            'fields': ('head_of_department', 'is_active')
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def programs_count(self, obj):
        """Display count of programs in this department"""
        count = obj.programs.filter(is_active=True).count()
        if count > 0:
            url = reverse('admin:academics_program_changelist') + f'?department__id={obj.id}'
            return format_html('<a href="{}">{} Programs</a>', url, count)
        return "0 Programs"
    programs_count.short_description = "Active Programs"


@admin.register(Program)
class ProgramAdmin(SimpleHistoryAdmin):
    list_display = ['name', 'code', 'program_type', 'department', 'duration_display', 
                   'max_students', 'is_active', 'created_at']
    list_filter = ['program_type', 'duration_type', 'department', 'is_active', 'created_at']
    search_fields = ['name', 'code', 'description']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['department', 'name']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'code', 'program_type', 'department')
        }),
        ('Duration', {
            'fields': ('duration_value', 'duration_type')
        }),
        ('Details', {
            'fields': ('description', 'prerequisites', 'learning_outcomes')
        }),
        ('Capacity', {
            'fields': ('max_students', 'min_students')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at', 'created_by'),
            'classes': ('collapse',)
        })
    )
    
    def duration_display(self, obj):
        """Display formatted duration"""
        return obj.get_duration_display()
    duration_display.short_description = "Duration"
    
    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(Session)
class SessionAdmin(SimpleHistoryAdmin):
    list_display = ['name', 'program', 'academic_year', 'session_type', 'start_date', 
                   'end_date', 'status', 'enrolled_count', 'capacity_status']
    list_filter = ['session_type', 'status', 'academic_year', 'program__department', 'created_at']
    search_fields = ['name', 'program__name', 'venue']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['-start_date', 'program']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'program', 'academic_year', 'session_type')
        }),
        ('Registration Dates', {
            'fields': ('registration_start_date', 'registration_end_date')
        }),
        ('Session Dates', {
            'fields': ('start_date', 'end_date')
        }),
        ('Capacity', {
            'fields': ('max_students', 'min_students')
        }),
        ('Details', {
            'fields': ('schedule_details', 'venue', 'instructor', 'status')
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at', 'created_by'),
            'classes': ('collapse',)
        })
    )
    
    def enrolled_count(self, obj):
        """Display current enrollment count"""
        count = obj.get_enrolled_count()
        return f"{count} students"
    enrolled_count.short_description = "Enrolled"
    
    def capacity_status(self, obj):
        """Display capacity status with color coding"""
        enrolled = obj.get_enrolled_count()
        max_capacity = obj.max_students
        percentage = (enrolled / max_capacity * 100) if max_capacity > 0 else 0
        
        if percentage >= 90:
            color = 'red'
            status = 'Full'
        elif percentage >= 70:
            color = 'orange'
            status = 'Nearly Full'
        else:
            color = 'green'
            status = 'Available'
            
        return format_html(
            '<span style="color: {};">{}/{} ({})</span>',
            color, enrolled, max_capacity, status
        )
    capacity_status.short_description = "Capacity"
    
    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(Course)
class CourseAdmin(SimpleHistoryAdmin):
    list_display = ['code', 'name', 'program', 'credit_hours', 'total_hours_display', 'is_active']
    list_filter = ['program', 'credit_hours', 'is_active', 'created_at']
    search_fields = ['name', 'code', 'description']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['program', 'code']
    filter_horizontal = ['prerequisites']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'code', 'program')
        }),
        ('Course Details', {
            'fields': ('description', 'credit_hours')
        }),
        ('Hours Breakdown', {
            'fields': ('theory_hours', 'practical_hours', 'lab_hours')
        }),
        ('Prerequisites', {
            'fields': ('prerequisites',)
        }),
        ('Content', {
            'fields': ('syllabus', 'learning_objectives', 'assessment_criteria'),
            'classes': ('collapse',)
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def total_hours_display(self, obj):
        """Display total hours breakdown"""
        return f"{obj.get_total_hours()}h (T:{obj.theory_hours}, P:{obj.practical_hours}, L:{obj.lab_hours})"
    total_hours_display.short_description = "Total Hours"


@admin.register(SessionCourse)
class SessionCourseAdmin(SimpleHistoryAdmin):
    list_display = ['session', 'course', 'instructor', 'start_date', 'end_date', 
                   'total_marks', 'passing_marks', 'is_active']
    list_filter = ['session__academic_year', 'session__program', 'is_active', 'created_at']
    search_fields = ['session__name', 'course__name', 'course__code', 'room']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['session', 'start_date', 'course']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('session', 'course', 'instructor')
        }),
        ('Schedule', {
            'fields': ('start_date', 'end_date', 'schedule_details', 'room')
        }),
        ('Assessment', {
            'fields': ('total_marks', 'passing_marks')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )


@admin.register(Campus)
class CampusAdmin(SimpleHistoryAdmin):
    list_display = ['name', 'code', 'city', 'province', 'total_capacity', 'classrooms_count', 'is_active']
    list_filter = ['province', 'city', 'is_active', 'created_at']
    search_fields = ['name', 'code', 'address', 'city']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['name']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'code', 'total_capacity')
        }),
        ('Location', {
            'fields': ('address', 'city', 'province')
        }),
        ('Contact Information', {
            'fields': ('phone_number', 'email')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def classrooms_count(self, obj):
        """Display count of classrooms in this campus"""
        count = obj.classrooms.filter(is_active=True).count()
        if count > 0:
            url = reverse('admin:academics_classroom_changelist') + f'?campus__id={obj.id}'
            return format_html('<a href="{}">{} Classrooms</a>', url, count)
        return "0 Classrooms"
    classrooms_count.short_description = "Classrooms"


@admin.register(Classroom)
class ClassroomAdmin(SimpleHistoryAdmin):
    list_display = ['name', 'campus', 'room_type', 'capacity', 'facilities_display', 'is_active']
    list_filter = ['campus', 'room_type', 'has_projector', 'has_whiteboard', 
                  'has_computer', 'has_ac', 'is_active', 'created_at']
    search_fields = ['name', 'campus__name']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['campus', 'name']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'campus', 'room_type', 'capacity')
        }),
        ('Facilities', {
            'fields': ('has_projector', 'has_whiteboard', 'has_computer', 'has_ac')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def facilities_display(self, obj):
        """Display available facilities as icons"""
        facilities = []
        if obj.has_projector:
            facilities.append('📽️ Projector')
        if obj.has_whiteboard:
            facilities.append('📝 Whiteboard')
        if obj.has_computer:
            facilities.append('💻 Computer')
        if obj.has_ac:
            facilities.append('❄️ AC')
        
        return ', '.join(facilities) if facilities else 'Basic'
    facilities_display.short_description = "Facilities"


# Customize admin site header
admin.site.site_header = "GIHD School Management System"
admin.site.site_title = "GIHD Admin"
admin.site.index_title = "Academic Management"