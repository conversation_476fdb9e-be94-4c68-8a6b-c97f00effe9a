# Generated by Django 5.2.4 on 2025-07-25 20:20

import django.core.validators
import django.db.models.deletion
import simple_history.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AcademicYear',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='e.g., 2024-2025', max_length=20, unique=True)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('is_active', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='Campus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('code', models.CharField(max_length=10, unique=True)),
                ('address', models.TextField()),
                ('city', models.CharField(max_length=50)),
                ('province', models.CharField(choices=[('Punjab', 'Punjab'), ('Sindh', 'Sindh'), ('KPK', 'Khyber Pakhtunkhwa'), ('Balochistan', 'Balochistan'), ('Gilgit-Baltistan', 'Gilgit-Baltistan'), ('AJK', 'Azad Jammu & Kashmir'), ('Islamabad', 'Islamabad Capital Territory')], max_length=50)),
                ('phone_number', models.CharField(blank=True, max_length=15, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('total_capacity', models.IntegerField(default=100)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('code', models.CharField(max_length=10, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('head_of_department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='headed_departments', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='HistoricalAcademicYear',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('name', models.CharField(db_index=True, help_text='e.g., 2024-2025', max_length=20)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('is_active', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical academic year',
                'verbose_name_plural': 'historical academic years',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalCampus',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('name', models.CharField(db_index=True, max_length=100)),
                ('code', models.CharField(db_index=True, max_length=10)),
                ('address', models.TextField()),
                ('city', models.CharField(max_length=50)),
                ('province', models.CharField(choices=[('Punjab', 'Punjab'), ('Sindh', 'Sindh'), ('KPK', 'Khyber Pakhtunkhwa'), ('Balochistan', 'Balochistan'), ('Gilgit-Baltistan', 'Gilgit-Baltistan'), ('AJK', 'Azad Jammu & Kashmir'), ('Islamabad', 'Islamabad Capital Territory')], max_length=50)),
                ('phone_number', models.CharField(blank=True, max_length=15, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('total_capacity', models.IntegerField(default=100)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical campus',
                'verbose_name_plural': 'historical campuss',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalClassroom',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('room_type', models.CharField(choices=[('Lecture', 'Lecture Hall'), ('Lab', 'Laboratory'), ('Computer', 'Computer Lab'), ('Workshop', 'Workshop'), ('Seminar', 'Seminar Room'), ('Other', 'Other')], max_length=15)),
                ('capacity', models.IntegerField()),
                ('has_projector', models.BooleanField(default=False)),
                ('has_whiteboard', models.BooleanField(default=False)),
                ('has_computer', models.BooleanField(default=False)),
                ('has_ac', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('campus', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='academics.campus')),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical classroom',
                'verbose_name_plural': 'historical classrooms',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalDepartment',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('name', models.CharField(db_index=True, max_length=100)),
                ('code', models.CharField(db_index=True, max_length=10)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('head_of_department', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical department',
                'verbose_name_plural': 'historical departments',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalProgram',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('code', models.CharField(db_index=True, max_length=20)),
                ('program_type', models.CharField(choices=[('Short Course', 'Short Course'), ('Certificate', 'Certificate Program'), ('Diploma', 'Diploma Program'), ('Workshop', 'Workshop'), ('Bootcamp', 'Bootcamp'), ('Other', 'Other')], max_length=20)),
                ('duration_value', models.IntegerField(help_text="Duration value (e.g., 6 for '6 months')", validators=[django.core.validators.MinValueValidator(1)])),
                ('duration_type', models.CharField(choices=[('Days', 'Days'), ('Weeks', 'Weeks'), ('Months', 'Months'), ('Years', 'Years')], max_length=10)),
                ('description', models.TextField()),
                ('prerequisites', models.TextField(blank=True, null=True)),
                ('learning_outcomes', models.TextField(blank=True, null=True)),
                ('max_students', models.IntegerField(default=30)),
                ('min_students', models.IntegerField(default=5)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('department', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='academics.department')),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical program',
                'verbose_name_plural': 'historical programs',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='Program',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('code', models.CharField(max_length=20, unique=True)),
                ('program_type', models.CharField(choices=[('Short Course', 'Short Course'), ('Certificate', 'Certificate Program'), ('Diploma', 'Diploma Program'), ('Workshop', 'Workshop'), ('Bootcamp', 'Bootcamp'), ('Other', 'Other')], max_length=20)),
                ('duration_value', models.IntegerField(help_text="Duration value (e.g., 6 for '6 months')", validators=[django.core.validators.MinValueValidator(1)])),
                ('duration_type', models.CharField(choices=[('Days', 'Days'), ('Weeks', 'Weeks'), ('Months', 'Months'), ('Years', 'Years')], max_length=10)),
                ('description', models.TextField()),
                ('prerequisites', models.TextField(blank=True, null=True)),
                ('learning_outcomes', models.TextField(blank=True, null=True)),
                ('max_students', models.IntegerField(default=30)),
                ('min_students', models.IntegerField(default=5)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='programs', to='academics.department')),
            ],
            options={
                'ordering': ['department', 'name'],
                'unique_together': {('name', 'department')},
            },
        ),
        migrations.CreateModel(
            name='HistoricalSession',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('session_type', models.CharField(choices=[('Regular', 'Regular Session'), ('Summer', 'Summer Session'), ('Winter', 'Winter Session'), ('Weekend', 'Weekend Session'), ('Evening', 'Evening Session'), ('Intensive', 'Intensive Session')], default='Regular', max_length=15)),
                ('registration_start_date', models.DateField()),
                ('registration_end_date', models.DateField()),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('max_students', models.IntegerField()),
                ('min_students', models.IntegerField(default=5)),
                ('status', models.CharField(choices=[('Planned', 'Planned'), ('Registration', 'Registration Open'), ('Active', 'Active'), ('Completed', 'Completed'), ('Cancelled', 'Cancelled')], default='Planned', max_length=15)),
                ('schedule_details', models.TextField(blank=True, help_text='Class timings, days of week, etc.', null=True)),
                ('venue', models.CharField(blank=True, max_length=200, null=True)),
                ('created_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('academic_year', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='academics.academicyear')),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('instructor', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('program', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='academics.program')),
            ],
            options={
                'verbose_name': 'historical session',
                'verbose_name_plural': 'historical sessions',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalCourse',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('code', models.CharField(db_index=True, max_length=20)),
                ('description', models.TextField()),
                ('credit_hours', models.IntegerField(default=3)),
                ('theory_hours', models.IntegerField(default=0)),
                ('practical_hours', models.IntegerField(default=0)),
                ('lab_hours', models.IntegerField(default=0)),
                ('syllabus', models.TextField(blank=True, null=True)),
                ('learning_objectives', models.TextField(blank=True, null=True)),
                ('assessment_criteria', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('program', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='academics.program')),
            ],
            options={
                'verbose_name': 'historical course',
                'verbose_name_plural': 'historical courses',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='Course',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('code', models.CharField(max_length=20, unique=True)),
                ('description', models.TextField()),
                ('credit_hours', models.IntegerField(default=3)),
                ('theory_hours', models.IntegerField(default=0)),
                ('practical_hours', models.IntegerField(default=0)),
                ('lab_hours', models.IntegerField(default=0)),
                ('syllabus', models.TextField(blank=True, null=True)),
                ('learning_objectives', models.TextField(blank=True, null=True)),
                ('assessment_criteria', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('prerequisites', models.ManyToManyField(blank=True, to='academics.course')),
                ('program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='courses', to='academics.program')),
            ],
            options={
                'ordering': ['program', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Session',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('session_type', models.CharField(choices=[('Regular', 'Regular Session'), ('Summer', 'Summer Session'), ('Winter', 'Winter Session'), ('Weekend', 'Weekend Session'), ('Evening', 'Evening Session'), ('Intensive', 'Intensive Session')], default='Regular', max_length=15)),
                ('registration_start_date', models.DateField()),
                ('registration_end_date', models.DateField()),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('max_students', models.IntegerField()),
                ('min_students', models.IntegerField(default=5)),
                ('status', models.CharField(choices=[('Planned', 'Planned'), ('Registration', 'Registration Open'), ('Active', 'Active'), ('Completed', 'Completed'), ('Cancelled', 'Cancelled')], default='Planned', max_length=15)),
                ('schedule_details', models.TextField(blank=True, help_text='Class timings, days of week, etc.', null=True)),
                ('venue', models.CharField(blank=True, max_length=200, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('academic_year', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sessions', to='academics.academicyear')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('instructor', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='instructed_sessions', to=settings.AUTH_USER_MODEL)),
                ('program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sessions', to='academics.program')),
            ],
            options={
                'ordering': ['-start_date', 'program'],
                'unique_together': {('name', 'program', 'academic_year')},
            },
        ),
        migrations.CreateModel(
            name='HistoricalSessionCourse',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('schedule_details', models.TextField(blank=True, null=True)),
                ('room', models.CharField(blank=True, max_length=50, null=True)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('total_marks', models.DecimalField(decimal_places=2, default=100.0, max_digits=5)),
                ('passing_marks', models.DecimalField(decimal_places=2, default=50.0, max_digits=5)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('course', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='academics.course')),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('instructor', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('session', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='academics.session')),
            ],
            options={
                'verbose_name': 'historical session course',
                'verbose_name_plural': 'historical session courses',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='Classroom',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('room_type', models.CharField(choices=[('Lecture', 'Lecture Hall'), ('Lab', 'Laboratory'), ('Computer', 'Computer Lab'), ('Workshop', 'Workshop'), ('Seminar', 'Seminar Room'), ('Other', 'Other')], max_length=15)),
                ('capacity', models.IntegerField()),
                ('has_projector', models.BooleanField(default=False)),
                ('has_whiteboard', models.BooleanField(default=False)),
                ('has_computer', models.BooleanField(default=False)),
                ('has_ac', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('campus', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='classrooms', to='academics.campus')),
            ],
            options={
                'ordering': ['campus', 'name'],
                'unique_together': {('name', 'campus')},
            },
        ),
        migrations.CreateModel(
            name='SessionCourse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('schedule_details', models.TextField(blank=True, null=True)),
                ('room', models.CharField(blank=True, max_length=50, null=True)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('total_marks', models.DecimalField(decimal_places=2, default=100.0, max_digits=5)),
                ('passing_marks', models.DecimalField(decimal_places=2, default=50.0, max_digits=5)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='session_courses', to='academics.course')),
                ('instructor', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='taught_courses', to=settings.AUTH_USER_MODEL)),
                ('session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='session_courses', to='academics.session')),
            ],
            options={
                'ordering': ['session', 'start_date', 'course'],
                'unique_together': {('session', 'course')},
            },
        ),
    ]
