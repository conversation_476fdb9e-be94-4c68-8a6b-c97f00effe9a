from rest_framework import generics, permissions, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django.db.models import Q, Count
from django.shortcuts import get_object_or_404
from common.mixins import StandardResponseMixin
from common.pagination import StandardPageNumberPagination
from .models import (
    AcademicYear, Department, Program, Session, Course, 
    SessionCourse, Campus, Classroom
)
from .serializers import (
    AcademicYearSerializer, DepartmentSerializer, ProgramSerializer,
    SessionSerializer, CourseSerializer, SessionCourseSerializer,
    CampusSerializer, ClassroomSerializer
)


# Academic Year Views
class AcademicYearListCreateView(StandardResponseMixin, generics.ListCreateAPIView):
    """List all academic years or create a new academic year"""
    queryset = AcademicYear.objects.all()
    serializer_class = AcademicYearSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = StandardPageNumberPagination
    
    def get_queryset(self):
        queryset = AcademicYear.objects.all()
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')
        return queryset.order_by('-start_date')


class AcademicYearDetailView(StandardResponseMixin, generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update or delete an academic year"""
    queryset = AcademicYear.objects.all()
    serializer_class = AcademicYearSerializer
    permission_classes = [permissions.IsAuthenticated]


# Department Views
class DepartmentListCreateView(StandardResponseMixin, generics.ListCreateAPIView):
    """List all departments or create a new department"""
    queryset = Department.objects.all()
    serializer_class = DepartmentSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = StandardPageNumberPagination
    
    def get_queryset(self):
        queryset = Department.objects.select_related('head_of_department')
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')
        return queryset.order_by('name')


class DepartmentDetailView(StandardResponseMixin, generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update or delete a department"""
    queryset = Department.objects.all()
    serializer_class = DepartmentSerializer
    permission_classes = [permissions.IsAuthenticated]


# Program Views
class ProgramListCreateView(StandardResponseMixin, generics.ListCreateAPIView):
    """List all programs or create a new program"""
    queryset = Program.objects.all()
    serializer_class = ProgramSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = StandardPageNumberPagination
    
    def get_queryset(self):
        queryset = Program.objects.select_related('department', 'created_by')
        
        # Filter by department
        department = self.request.query_params.get('department')
        if department:
            queryset = queryset.filter(department_id=department)
        
        # Filter by program type
        program_type = self.request.query_params.get('program_type')
        if program_type:
            queryset = queryset.filter(program_type=program_type)
        
        # Filter by active status
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')
        
        return queryset.order_by('department', 'name')
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)


class ProgramDetailView(StandardResponseMixin, generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update or delete a program"""
    queryset = Program.objects.all()
    serializer_class = ProgramSerializer
    permission_classes = [permissions.IsAuthenticated]


# Session Views
class SessionListCreateView(StandardResponseMixin, generics.ListCreateAPIView):
    """List all sessions or create a new session"""
    queryset = Session.objects.all()
    serializer_class = SessionSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = StandardPageNumberPagination
    
    def get_queryset(self):
        queryset = Session.objects.select_related(
            'program', 'academic_year', 'instructor', 'created_by'
        )
        
        # Filter by program
        program = self.request.query_params.get('program')
        if program:
            queryset = queryset.filter(program_id=program)
        
        # Filter by academic year
        academic_year = self.request.query_params.get('academic_year')
        if academic_year:
            queryset = queryset.filter(academic_year_id=academic_year)
        
        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        # Filter by session type
        session_type = self.request.query_params.get('session_type')
        if session_type:
            queryset = queryset.filter(session_type=session_type)
        
        return queryset.order_by('-start_date', 'program')
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)


class SessionDetailView(StandardResponseMixin, generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update or delete a session"""
    queryset = Session.objects.all()
    serializer_class = SessionSerializer
    permission_classes = [permissions.IsAuthenticated]


# Course Views
class CourseListCreateView(StandardResponseMixin, generics.ListCreateAPIView):
    """List all courses or create a new course"""
    queryset = Course.objects.all()
    serializer_class = CourseSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = StandardPageNumberPagination
    
    def get_queryset(self):
        queryset = Course.objects.select_related('program').prefetch_related('prerequisites')
        
        # Filter by program
        program = self.request.query_params.get('program')
        if program:
            queryset = queryset.filter(program_id=program)
        
        # Filter by active status
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')
        
        return queryset.order_by('program', 'name')


class CourseDetailView(StandardResponseMixin, generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update or delete a course"""
    queryset = Course.objects.all()
    serializer_class = CourseSerializer
    permission_classes = [permissions.IsAuthenticated]


# Session Course Views
class SessionCourseListCreateView(StandardResponseMixin, generics.ListCreateAPIView):
    """List courses for a session or assign a course to a session"""
    serializer_class = SessionCourseSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        session_id = self.kwargs['session_id']
        return SessionCourse.objects.filter(
            session_id=session_id
        ).select_related('session', 'course', 'instructor')
    
    def perform_create(self, serializer):
        session_id = self.kwargs['session_id']
        session = get_object_or_404(Session, id=session_id)
        serializer.save(session=session)


class SessionCourseDetailView(StandardResponseMixin, generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update or delete a session course"""
    serializer_class = SessionCourseSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        session_id = self.kwargs['session_id']
        return SessionCourse.objects.filter(session_id=session_id)


# Campus Views
class CampusListCreateView(StandardResponseMixin, generics.ListCreateAPIView):
    """List all campuses or create a new campus"""
    queryset = Campus.objects.all()
    serializer_class = CampusSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = StandardPageNumberPagination
    
    def get_queryset(self):
        queryset = Campus.objects.all()
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')
        return queryset.order_by('name')


class CampusDetailView(StandardResponseMixin, generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update or delete a campus"""
    queryset = Campus.objects.all()
    serializer_class = CampusSerializer
    permission_classes = [permissions.IsAuthenticated]


# Classroom Views
class ClassroomListCreateView(StandardResponseMixin, generics.ListCreateAPIView):
    """List all classrooms or create a new classroom"""
    queryset = Classroom.objects.all()
    serializer_class = ClassroomSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = StandardPageNumberPagination
    
    def get_queryset(self):
        queryset = Classroom.objects.select_related('campus')
        
        # Filter by campus
        campus = self.request.query_params.get('campus')
        if campus:
            queryset = queryset.filter(campus_id=campus)
        
        # Filter by room type
        room_type = self.request.query_params.get('room_type')
        if room_type:
            queryset = queryset.filter(room_type=room_type)
        
        # Filter by active status
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')
        
        return queryset.order_by('campus', 'name')


class ClassroomDetailView(StandardResponseMixin, generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update or delete a classroom"""
    queryset = Classroom.objects.all()
    serializer_class = ClassroomSerializer
    permission_classes = [permissions.IsAuthenticated]


# Statistics Views
class AcademicsStatsView(StandardResponseMixin, APIView):
    """Get academics statistics"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        stats = {}
        
        # Basic counts
        stats['total_programs'] = Program.objects.filter(is_active=True).count()
        stats['active_sessions'] = Session.objects.filter(status='Active').count()
        stats['total_courses'] = Course.objects.filter(is_active=True).count()
        stats['total_departments'] = Department.objects.filter(is_active=True).count()
        stats['total_campuses'] = Campus.objects.filter(is_active=True).count()
        stats['total_classrooms'] = Classroom.objects.filter(is_active=True).count()
        
        # Session status distribution
        session_stats = Session.objects.values('status').annotate(
            count=Count('id')
        ).order_by('-count')
        stats['session_status_distribution'] = list(session_stats)
        
        # Program type distribution
        program_stats = Program.objects.filter(is_active=True).values('program_type').annotate(
            count=Count('id')
        ).order_by('-count')
        stats['program_type_distribution'] = list(program_stats)
        
        # Department program counts
        department_stats = Department.objects.filter(is_active=True).annotate(
            program_count=Count('programs', filter=Q(programs__is_active=True))
        ).values('name', 'program_count').order_by('-program_count')
        stats['department_distribution'] = list(department_stats)
        
        return self.success_response(
            data=stats,
            message="Academic statistics retrieved successfully"
        )