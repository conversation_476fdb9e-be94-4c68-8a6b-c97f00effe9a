from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    AcademicYear, Department, Program, Session, Course, 
    SessionCourse, Campus, Classroom
)

User = get_user_model()


class AcademicYearSerializer(serializers.ModelSerializer):
    """Serializer for Academic Year model"""
    
    class Meta:
        model = AcademicYear
        fields = [
            'id', 'name', 'start_date', 'end_date', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def validate(self, data):
        if data['start_date'] >= data['end_date']:
            raise serializers.ValidationError("Start date must be before end date")
        return data


class DepartmentSerializer(serializers.ModelSerializer):
    """Serializer for Department model"""
    head_of_department_name = serializers.CharField(
        source='head_of_department.get_full_name', 
        read_only=True
    )
    program_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Department
        fields = [
            'id', 'name', 'code', 'description', 'head_of_department',
            'head_of_department_name', 'is_active', 'program_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'program_count']

    def get_program_count(self, obj):
        return obj.programs.filter(is_active=True).count()


class ProgramSerializer(serializers.ModelSerializer):
    """Serializer for Program model"""
    department_name = serializers.CharField(source='department.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    duration_display = serializers.CharField(source='get_duration_display', read_only=True)
    session_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Program
        fields = [
            'id', 'name', 'code', 'program_type', 'department', 'department_name',
            'duration_value', 'duration_type', 'duration_display', 'description',
            'prerequisites', 'learning_outcomes', 'max_students', 'min_students',
            'is_active', 'session_count', 'created_at', 'updated_at', 
            'created_by', 'created_by_name'
        ]
        read_only_fields = [
            'id', 'created_at', 'updated_at', 'created_by', 'session_count'
        ]

    def get_session_count(self, obj):
        return obj.sessions.count()


class SessionSerializer(serializers.ModelSerializer):
    """Serializer for Session model"""
    program_name = serializers.CharField(source='program.name', read_only=True)
    academic_year_name = serializers.CharField(source='academic_year.name', read_only=True)
    instructor_name = serializers.CharField(source='instructor.get_full_name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    enrolled_count = serializers.SerializerMethodField()
    is_registration_open = serializers.BooleanField(read_only=True)
    is_active = serializers.BooleanField(read_only=True)
    has_capacity = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = Session
        fields = [
            'id', 'name', 'program', 'program_name', 'academic_year', 'academic_year_name',
            'session_type', 'registration_start_date', 'registration_end_date',
            'start_date', 'end_date', 'max_students', 'min_students', 'status',
            'schedule_details', 'venue', 'instructor', 'instructor_name',
            'enrolled_count', 'is_registration_open', 'is_active', 'has_capacity',
            'created_at', 'updated_at', 'created_by', 'created_by_name'
        ]
        read_only_fields = [
            'id', 'created_at', 'updated_at', 'created_by', 'enrolled_count',
            'is_registration_open', 'is_active', 'has_capacity'
        ]

    def get_enrolled_count(self, obj):
        return obj.get_enrolled_count()

    def validate(self, data):
        # Validate registration dates
        if data['registration_start_date'] >= data['registration_end_date']:
            raise serializers.ValidationError(
                "Registration start date must be before registration end date"
            )
        
        # Validate session dates
        if data['start_date'] >= data['end_date']:
            raise serializers.ValidationError("Start date must be before end date")
        
        # Validate registration end date vs session start date
        if data['registration_end_date'] > data['start_date']:
            raise serializers.ValidationError(
                "Registration must end before session starts"
            )
        
        return data


class CourseSerializer(serializers.ModelSerializer):
    """Serializer for Course model"""
    program_name = serializers.CharField(source='program.name', read_only=True)
    prerequisites_names = serializers.SerializerMethodField()
    total_hours = serializers.IntegerField(source='get_total_hours', read_only=True)
    
    class Meta:
        model = Course
        fields = [
            'id', 'name', 'code', 'program', 'program_name', 'description',
            'credit_hours', 'theory_hours', 'practical_hours', 'lab_hours',
            'total_hours', 'prerequisites', 'prerequisites_names', 'syllabus',
            'learning_objectives', 'assessment_criteria', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'total_hours']

    def get_prerequisites_names(self, obj):
        return [prereq.name for prereq in obj.prerequisites.all()]


class SessionCourseSerializer(serializers.ModelSerializer):
    """Serializer for Session Course model"""
    session_name = serializers.CharField(source='session.name', read_only=True)
    course_name = serializers.CharField(source='course.name', read_only=True)
    course_code = serializers.CharField(source='course.code', read_only=True)
    instructor_name = serializers.CharField(source='instructor.get_full_name', read_only=True)
    
    class Meta:
        model = SessionCourse
        fields = [
            'id', 'session', 'session_name', 'course', 'course_name', 'course_code',
            'instructor', 'instructor_name', 'schedule_details', 'room',
            'start_date', 'end_date', 'total_marks', 'passing_marks',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def validate(self, data):
        if data['start_date'] >= data['end_date']:
            raise serializers.ValidationError("Start date must be before end date")
        
        if data['passing_marks'] >= data['total_marks']:
            raise serializers.ValidationError(
                "Passing marks must be less than total marks"
            )
        
        return data


class CampusSerializer(serializers.ModelSerializer):
    """Serializer for Campus model"""
    classroom_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Campus
        fields = [
            'id', 'name', 'code', 'address', 'city', 'province',
            'phone_number', 'email', 'total_capacity', 'classroom_count',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'classroom_count']

    def get_classroom_count(self, obj):
        return obj.classrooms.filter(is_active=True).count()


class ClassroomSerializer(serializers.ModelSerializer):
    """Serializer for Classroom model"""
    campus_name = serializers.CharField(source='campus.name', read_only=True)
    campus_city = serializers.CharField(source='campus.city', read_only=True)
    facilities = serializers.SerializerMethodField()
    
    class Meta:
        model = Classroom
        fields = [
            'id', 'name', 'campus', 'campus_name', 'campus_city', 'room_type',
            'capacity', 'has_projector', 'has_whiteboard', 'has_computer',
            'has_ac', 'facilities', 'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'facilities']

    def get_facilities(self, obj):
        facilities = []
        if obj.has_projector:
            facilities.append('Projector')
        if obj.has_whiteboard:
            facilities.append('Whiteboard')
        if obj.has_computer:
            facilities.append('Computer')
        if obj.has_ac:
            facilities.append('Air Conditioning')
        return facilities