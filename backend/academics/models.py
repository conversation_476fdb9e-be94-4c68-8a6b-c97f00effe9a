from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from simple_history.models import HistoricalRecords
from decimal import Decimal
from datetime import date, timedelta


class AcademicYear(models.Model):
    name = models.CharField(max_length=20, unique=True, help_text="e.g., 2024-2025")
    start_date = models.DateField()
    end_date = models.DateField()
    is_active = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # History tracking
    history = HistoricalRecords()
    
    class Meta:
        ordering = ['-start_date']
    
    def __str__(self):
        return self.name
    
    def save(self, *args, **kwargs):
        if self.is_active:
            AcademicYear.objects.filter(is_active=True).update(is_active=False)
        super().save(*args, **kwargs)


class Department(models.Model):
    name = models.Char<PERSON>ield(max_length=100, unique=True)
    code = models.CharField(max_length=10, unique=True)
    description = models.TextField(null=True, blank=True)
    head_of_department = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='headed_departments'
    )
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # History tracking
    history = HistoricalRecords()
    
    class Meta:
        ordering = ['name']
    
    def __str__(self):
        return f"{self.name} ({self.code})"


class Program(models.Model):
    PROGRAM_TYPE_CHOICES = [
        ('Short Course', 'Short Course'),
        ('Certificate', 'Certificate Program'),
        ('Diploma', 'Diploma Program'),
        ('Workshop', 'Workshop'),
        ('Bootcamp', 'Bootcamp'),
        ('Other', 'Other'),
    ]
    
    DURATION_TYPE_CHOICES = [
        ('Days', 'Days'),
        ('Weeks', 'Weeks'),
        ('Months', 'Months'),
        ('Years', 'Years'),
    ]
    
    name = models.CharField(max_length=200)
    code = models.CharField(max_length=20, unique=True)
    program_type = models.CharField(max_length=20, choices=PROGRAM_TYPE_CHOICES)
    department = models.ForeignKey(Department, on_delete=models.CASCADE, related_name='programs')
    
    # Dynamic Duration System
    duration_value = models.IntegerField(
        validators=[MinValueValidator(1)],
        help_text="Duration value (e.g., 6 for '6 months')"
    )
    duration_type = models.CharField(max_length=10, choices=DURATION_TYPE_CHOICES)
    
    description = models.TextField()
    prerequisites = models.TextField(null=True, blank=True)
    learning_outcomes = models.TextField(null=True, blank=True)
    
    # Capacity and Pricing
    max_students = models.IntegerField(default=30)
    min_students = models.IntegerField(default=5)
    
    # Status
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    
    # History tracking
    history = HistoricalRecords()
    
    class Meta:
        ordering = ['department', 'name']
        unique_together = [['name', 'department']]
    
    def __str__(self):
        return f"{self.name} ({self.get_duration_display()})"
    
    def get_duration_display(self):
        return f"{self.duration_value} {self.duration_type}"
    
    def get_duration_in_days(self):
        multipliers = {
            'Days': 1,
            'Weeks': 7,
            'Months': 30,  # Approximate
            'Years': 365,  # Approximate
        }
        return self.duration_value * multipliers.get(self.duration_type, 1)


class Session(models.Model):
    SESSION_TYPE_CHOICES = [
        ('Regular', 'Regular Session'),
        ('Summer', 'Summer Session'),
        ('Winter', 'Winter Session'),
        ('Weekend', 'Weekend Session'),
        ('Evening', 'Evening Session'),
        ('Intensive', 'Intensive Session'),
    ]
    
    STATUS_CHOICES = [
        ('Planned', 'Planned'),
        ('Registration', 'Registration Open'),
        ('Active', 'Active'),
        ('Completed', 'Completed'),
        ('Cancelled', 'Cancelled'),
    ]
    
    name = models.CharField(max_length=100)
    program = models.ForeignKey(Program, on_delete=models.CASCADE, related_name='sessions')
    academic_year = models.ForeignKey(AcademicYear, on_delete=models.CASCADE, related_name='sessions')
    session_type = models.CharField(max_length=15, choices=SESSION_TYPE_CHOICES, default='Regular')
    
    # Dates
    registration_start_date = models.DateField()
    registration_end_date = models.DateField()
    start_date = models.DateField()
    end_date = models.DateField()
    
    # Capacity
    max_students = models.IntegerField()
    min_students = models.IntegerField(default=5)
    
    # Status
    status = models.CharField(max_length=15, choices=STATUS_CHOICES, default='Planned')
    
    # Additional Information
    schedule_details = models.TextField(null=True, blank=True, help_text="Class timings, days of week, etc.")
    venue = models.CharField(max_length=200, null=True, blank=True)
    instructor = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='instructed_sessions'
    )
    
    # System Fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    
    # History tracking
    history = HistoricalRecords()
    
    class Meta:
        ordering = ['-start_date', 'program']
        unique_together = [['name', 'program', 'academic_year']]
    
    def __str__(self):
        return f"{self.name} - {self.program.name} ({self.academic_year.name})"
    
    def is_registration_open(self):
        today = date.today()
        return (self.registration_start_date <= today <= self.registration_end_date 
                and self.status == 'Registration')
    
    def is_active(self):
        today = date.today()
        return self.start_date <= today <= self.end_date and self.status == 'Active'
    
    def get_enrolled_count(self):
        return self.enrollments.filter(status='Enrolled').count()
    
    def has_capacity(self):
        return self.get_enrolled_count() < self.max_students
    
    def save(self, *args, **kwargs):
        if not self.max_students:
            self.max_students = self.program.max_students
        if not self.min_students:
            self.min_students = self.program.min_students
        super().save(*args, **kwargs)


class Course(models.Model):
    name = models.CharField(max_length=200)
    code = models.CharField(max_length=20, unique=True)
    program = models.ForeignKey(Program, on_delete=models.CASCADE, related_name='courses')
    
    # Course Details
    description = models.TextField()
    credit_hours = models.IntegerField(default=3)
    theory_hours = models.IntegerField(default=0)
    practical_hours = models.IntegerField(default=0)
    lab_hours = models.IntegerField(default=0)
    
    # Prerequisites
    prerequisites = models.ManyToManyField('self', blank=True, symmetrical=False)
    
    # Content
    syllabus = models.TextField(null=True, blank=True)
    learning_objectives = models.TextField(null=True, blank=True)
    assessment_criteria = models.TextField(null=True, blank=True)
    
    # Status
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # History tracking
    history = HistoricalRecords()
    
    class Meta:
        ordering = ['program', 'name']
    
    def __str__(self):
        return f"{self.code} - {self.name}"
    
    def get_total_hours(self):
        return self.theory_hours + self.practical_hours + self.lab_hours


class SessionCourse(models.Model):
    session = models.ForeignKey(Session, on_delete=models.CASCADE, related_name='session_courses')
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='session_courses')
    instructor = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='taught_courses'
    )
    
    # Schedule
    schedule_details = models.TextField(null=True, blank=True)
    room = models.CharField(max_length=50, null=True, blank=True)
    
    # Dates specific to this course in the session
    start_date = models.DateField()
    end_date = models.DateField()
    
    # Assessment
    total_marks = models.DecimalField(max_digits=5, decimal_places=2, default=100.00)
    passing_marks = models.DecimalField(max_digits=5, decimal_places=2, default=50.00)
    
    # Status
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # History tracking
    history = HistoricalRecords()
    
    class Meta:
        unique_together = [['session', 'course']]
        ordering = ['session', 'start_date', 'course']
    
    def __str__(self):
        return f"{self.course.name} in {self.session.name}"


class Campus(models.Model):
    name = models.CharField(max_length=100, unique=True)
    code = models.CharField(max_length=10, unique=True)
    address = models.TextField()
    city = models.CharField(max_length=50)
    province = models.CharField(max_length=50, choices=[
        ('Punjab', 'Punjab'),
        ('Sindh', 'Sindh'),
        ('KPK', 'Khyber Pakhtunkhwa'),
        ('Balochistan', 'Balochistan'),
        ('Gilgit-Baltistan', 'Gilgit-Baltistan'),
        ('AJK', 'Azad Jammu & Kashmir'),
        ('Islamabad', 'Islamabad Capital Territory'),
    ])
    
    # Contact Information
    phone_number = models.CharField(max_length=15, null=True, blank=True)
    email = models.EmailField(null=True, blank=True)
    
    # Capacity
    total_capacity = models.IntegerField(default=100)
    
    # Status
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # History tracking
    history = HistoricalRecords()
    
    class Meta:
        ordering = ['name']
    
    def __str__(self):
        return f"{self.name} ({self.city})"


class Classroom(models.Model):
    ROOM_TYPE_CHOICES = [
        ('Lecture', 'Lecture Hall'),
        ('Lab', 'Laboratory'),
        ('Computer', 'Computer Lab'),
        ('Workshop', 'Workshop'),
        ('Seminar', 'Seminar Room'),
        ('Other', 'Other'),
    ]
    
    name = models.CharField(max_length=50)
    campus = models.ForeignKey(Campus, on_delete=models.CASCADE, related_name='classrooms')
    room_type = models.CharField(max_length=15, choices=ROOM_TYPE_CHOICES)
    capacity = models.IntegerField()
    
    # Facilities
    has_projector = models.BooleanField(default=False)
    has_whiteboard = models.BooleanField(default=False)
    has_computer = models.BooleanField(default=False)
    has_ac = models.BooleanField(default=False)
    
    # Status
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # History tracking
    history = HistoricalRecords()
    
    class Meta:
        unique_together = [['name', 'campus']]
        ordering = ['campus', 'name']
    
    def __str__(self):
        return f"{self.name} - {self.campus.name}"
