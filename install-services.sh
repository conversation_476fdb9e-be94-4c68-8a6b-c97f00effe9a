#!/bin/bash

# GIHD School Management System Service Installation Script
# This script installs and configures systemctl services for both frontend and backend

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root. Please run as the user who owns the application files."
   exit 1
fi

# Check if systemd is available
if ! command -v systemctl &> /dev/null; then
    print_error "systemctl is not available. This script requires systemd."
    exit 1
fi

# Get the current directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$SCRIPT_DIR"

print_status "Installing GIHD School Management System services..."
print_status "Project directory: $PROJECT_DIR"

# Check if service files exist
if [[ ! -f "$PROJECT_DIR/gihd-backend.service" ]]; then
    print_error "Backend service file not found: $PROJECT_DIR/gihd-backend.service"
    exit 1
fi

if [[ ! -f "$PROJECT_DIR/gihd-frontend.service" ]]; then
    print_error "Frontend service file not found: $PROJECT_DIR/gihd-frontend.service"
    exit 1
fi

# Copy service files to systemd directory
print_status "Copying service files to /etc/systemd/system/..."
sudo cp "$PROJECT_DIR/gihd-backend.service" /etc/systemd/system/
sudo cp "$PROJECT_DIR/gihd-frontend.service" /etc/systemd/system/

# Set proper permissions
sudo chmod 644 /etc/systemd/system/gihd-backend.service
sudo chmod 644 /etc/systemd/system/gihd-frontend.service

# Reload systemd daemon
print_status "Reloading systemd daemon..."
sudo systemctl daemon-reload

# Enable services to start on boot
print_status "Enabling services to start on boot..."
sudo systemctl enable gihd-backend.service
sudo systemctl enable gihd-frontend.service

# Check if virtual environment exists for backend
if [[ ! -d "$PROJECT_DIR/backend/venv" ]]; then
    print_warning "Virtual environment not found at $PROJECT_DIR/backend/venv"
    print_warning "Creating virtual environment for backend..."
    cd "$PROJECT_DIR/backend"
    python3 -m venv venv
    source venv/bin/activate
    pip install -r requirements.txt
    print_status "Virtual environment created and dependencies installed"
fi

# Check if node_modules exists for frontend
if [[ ! -d "$PROJECT_DIR/frontend/node_modules" ]]; then
    print_warning "node_modules not found at $PROJECT_DIR/frontend/node_modules"
    print_warning "Installing frontend dependencies..."
    cd "$PROJECT_DIR/frontend"
    npm install
    print_status "Frontend dependencies installed"
fi

# Start the services
print_status "Starting services..."
sudo systemctl start gihd-backend.service
sudo systemctl start gihd-frontend.service

# Check service status
print_status "Checking service status..."

echo ""
echo "=== Backend Service Status ==="
sudo systemctl status gihd-backend.service --no-pager || true

echo ""
echo "=== Frontend Service Status ==="
sudo systemctl status gihd-frontend.service --no-pager || true

echo ""
print_status "Installation completed!"
print_status "Backend is running on: http://localhost:5001"
print_status "Frontend is running on: http://localhost:5000"

echo ""
echo "Useful commands:"
echo "  Start services:    sudo systemctl start gihd-backend gihd-frontend"
echo "  Stop services:     sudo systemctl stop gihd-backend gihd-frontend"
echo "  Restart services:  sudo systemctl restart gihd-backend gihd-frontend"
echo "  Check status:      sudo systemctl status gihd-backend gihd-frontend"
echo "  View logs:         sudo journalctl -u gihd-backend -f"
echo "                     sudo journalctl -u gihd-frontend -f"
echo "  Disable services:  sudo systemctl disable gihd-backend gihd-frontend"

print_status "Services installed and started successfully!"