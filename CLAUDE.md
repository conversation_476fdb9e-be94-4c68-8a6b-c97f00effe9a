# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

GIHD School Management System (SMS) is a full-stack web application for managing student information, fees, academics, and administrative tasks. Built with Django REST Framework backend and Next.js frontend, optimized for Pakistani educational institutions.

### Tech Stack
- **Backend**: Django 5.2.4 + Django REST Framework + SQLite
- **Frontend**: Next.js 15.2.4 + React 19 + TypeScript + Tailwind CSS + ShadCN UI
- **Authentication**: JWT with Simple JWT
- **Testing**: Playwright for E2E testing
- **UI Components**: Radix UI primitives with custom ShadCN components

## Development Commands

### Backend Commands (Django)
```bash
cd backend
python manage.py runserver                    # Start development server (port 8000)
python manage.py migrate                      # Apply database migrations
python manage.py makemigrations               # Create new migrations
python manage.py createsuperuser              # Create admin user
python manage.py shell                        # Django shell for testing
python manage.py test                         # Run Django tests
python manage.py collectstatic               # Collect static files
python manage.py create_test_data             # Create sample data for testing
```

### Frontend Commands (Next.js)
```bash
cd frontend
npm run dev                                   # Start development server (port 3000)
npm run build                                # Build for production
npm run start                                # Start production server
npm run lint                                 # Run ESLint
npm run test                                 # Run Playwright tests
npm run test:ui                              # Run tests with UI
npm run test:debug                           # Debug tests
npm run test:headed                          # Run tests in headed mode
npm run test:codegen                         # Generate test code
```

### Database Management
```bash
cd backend
python manage.py flush                       # Clear all data (keep schema)
python manage.py dbshell                     # Access SQLite shell
python manage.py dumpdata > backup.json     # Backup data
python manage.py loaddata backup.json       # Restore data
```

## Architecture

### Backend Structure (Django)
- **gihd_sms/**: Main project configuration
  - `settings.py`: Django settings with JWT, CORS, custom pagination
  - `urls.py`: Main URL routing with API prefix `/api/`
- **common/**: Shared utilities, mixins, pagination, exception handlers
- **authentication/**: JWT authentication, user management, role-based permissions
- **students/**: Student and Guardian models with Pakistani-specific fields (CNIC, provinces)
- **academics/**: Academic year, sessions, subjects, grades management
- **fees/**: Fee structures, payments, financial tracking
- **documents/**: Document management and file uploads
- **reports/**: Report generation, bulk operations, data export

### Frontend Structure (Next.js)
- **app/**: Next.js 13+ app router with nested layouts
- **components/**: Reusable UI components
  - `ui/`: ShadCN UI primitives (buttons, forms, tables, etc.)
  - `auth/`: Authentication components and guards
- **services/**: API service classes with TypeScript interfaces
- **contexts/**: React contexts for state management (auth, theme)
- **hooks/**: Custom React hooks
- **tests/**: Playwright E2E tests with page objects and utilities

### Key Features
- **Role-based Authentication**: Admin, Teacher, Staff, Parent roles with granular permissions
- **Pakistani Localization**: CNIC validation, Pakistani provinces, phone number formats
- **Student Management**: Complete student profiles with guardians, medical info, documents
- **Fee Management**: Flexible fee structures, payment tracking, financial reports
- **Academic Management**: Terms, subjects, grades, attendance tracking
- **Document Management**: File uploads with automatic resizing and validation
- **Audit Trail**: Historical tracking of all data changes using django-simple-history
- **Responsive Design**: Mobile-first design with ShadCN UI components

## Database Schema

### Core Models
- **Student**: Comprehensive student information with Pakistani-specific fields
  - Auto-generated student IDs: `GIHD{YEAR}{0001}`
  - CNIC validation: `12345-1234567-1` format
  - Pakistani provinces and cities
  - Photo auto-resizing to 400x400px
- **Guardian**: Multiple guardians per student with relationship types
- **User**: Extended Django user with roles and permissions
- **Fee Structures**: Flexible fee management with multiple payment types
- **Academic Sessions**: Year-based academic structure

## API Architecture

### Authentication Flow
- JWT-based authentication with access/refresh tokens
- Custom permission classes and decorators
- Role-based access control (RBAC)
- Session management with device tracking

### API Endpoints Structure
```
/api/auth/          # Authentication endpoints
/api/students/      # Student management
/api/academics/     # Academic year, sessions, subjects
/api/fees/          # Fee structures and payments
/api/documents/     # Document management
/api/reports/       # Reports and bulk operations
```

### Response Format
All API responses follow this standardized format:
```typescript
{
  status: 'success' | 'error',
  http_code: number,
  message: string,
  data?: any,
  errors?: Record<string, string[]>
}
```

## Frontend Architecture

### Service Layer Pattern
- **Base API Service**: Centralized HTTP client with authentication
- **Domain Services**: Type-safe API wrappers (AuthService, StudentService, etc.)
- **Response Handling**: Standardized error handling and loading states

### Authentication System
- JWT token management with automatic refresh
- Protected routes with role-based access
- Context-based auth state management
- Persistent authentication across sessions

### UI Component System
- ShadCN UI with custom theme support
- Form validation using React Hook Form + Zod
- Responsive design with Tailwind CSS
- Dark/light mode support

## Testing Strategy

### Playwright E2E Testing
- **Page Object Model**: Organized test structure
- **Global Setup/Teardown**: Test environment management
- **Mock API Server**: Isolated testing with mock data
- **Multi-browser Testing**: Chrome, Firefox, Safari, Mobile
- **Visual Testing**: Screenshot comparison on failures

### Test Categories
- Authentication flows and role-based access
- Student management CRUD operations
- Form validation and error handling
- Navigation and responsive design
- API integration testing

## Development Workflow

### Setting Up Development Environment
1. **Backend Setup**:
   ```bash
   cd backend
   python -m venv venv
   source venv/bin/activate  # Linux/Mac
   # venv\Scripts\activate   # Windows
   pip install -r requirements.txt
   python manage.py migrate
   python manage.py createsuperuser
   python manage.py create_test_data
   ```

2. **Frontend Setup**:
   ```bash
   cd frontend
   npm install
   # Copy .env.local and configure API endpoints
   ```

3. **Start Development Servers**:
   ```bash
   # Terminal 1 (Backend)
   cd backend && python manage.py runserver

   # Terminal 2 (Frontend)
   cd frontend && npm run dev
   ```

### Code Style and Conventions
- **Backend**: Follow Django conventions, use class-based views, comprehensive docstrings
- **Frontend**: TypeScript strict mode, consistent component patterns, hooks for state
- **API**: RESTful design with consistent response formats
- **Database**: Use migrations for all schema changes, index frequently queried fields

### Security Considerations
- JWT tokens in httpOnly cookies (production)
- CNIC and sensitive data validation
- File upload restrictions and scanning
- Rate limiting on authentication endpoints
- CORS configuration for frontend domains

## Pakistani Education System Features

### Student ID Generation
- Format: `GIHD{YEAR}{0001}` (e.g., GIHD20240001)
- Auto-incremented within each academic year
- Unique across the entire system

### Pakistani-Specific Validations
- **CNIC**: `12345-1234567-1` format validation
- **Phone Numbers**: `+923001234567` or `03001234567` formats
- **Provinces**: All Pakistani provinces and territories
- **Education Levels**: Matric, Intermediate, Bachelor's, Master's

### Guardian System
- Multiple guardians per student with relationship types
- Emergency contacts and pickup authorization
- Income tracking for fee assessment
- Communication preference management

## Deployment and Production

### Environment Configuration
- Use environment variables for sensitive settings
- Separate settings files for development/production
- Static file serving configuration
- Database connection pooling

### Production Checklist
- Set `DEBUG = False` in Django settings
- Configure proper `ALLOWED_HOSTS`
- Set up proper static file serving
- Enable database connection pooling
- Configure logging and monitoring
- Set up automated backups

This system is designed specifically for Pakistani educational institutions with localized features and compliance requirements.

Most important the backend development server is running port 5001
Most important the frontend development server is running port 5000

Please resart after every change
