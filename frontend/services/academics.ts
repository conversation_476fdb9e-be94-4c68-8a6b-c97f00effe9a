import { BaseApiService } from './base-api';

export interface Subject {
  id?: number;
  name: string;
  code: string;
  description?: string;
  class_level: string;
  credits?: number;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface Class {
  id?: number;
  name: string;
  level: string;
  section: string;
  academic_year: string;
  class_teacher?: number;
  subjects: number[];
  student_count?: number;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface AcademicYear {
  id?: number;
  name: string;
  start_date: string;
  end_date: string;
  is_current: boolean;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface Grade {
  id?: number;
  student: number;
  subject: number;
  class: number;
  exam_type: string;
  marks_obtained: number;
  total_marks: number;
  grade: string;
  remarks?: string;
  exam_date: string;
  created_at?: string;
  updated_at?: string;
}

export interface Attendance {
  id?: number;
  student: number;
  class: number;
  date: string;
  status: 'Present' | 'Absent' | 'Late' | 'Excused';
  remarks?: string;
  created_at?: string;
  updated_at?: string;
}

export class AcademicsService extends BaseApiService {
  private readonly subjectsEndpoint = '/academics/subjects';
  private readonly classesEndpoint = '/academics/classes';
  private readonly academicYearsEndpoint = '/academics/academic-years';
  private readonly gradesEndpoint = '/academics/grades';
  private readonly attendanceEndpoint = '/academics/attendance';

  // Subjects
  async getSubjects(params?: { class_level?: string; is_active?: boolean }) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    const query = queryParams.toString();
    const url = query ? `${this.subjectsEndpoint}/?${query}` : `${this.subjectsEndpoint}/`;
    return this.get<Subject[]>(url);
  }

  async getSubject(id: number) {
    return this.get<Subject>(`${this.subjectsEndpoint}/${id}/`);
  }

  async createSubject(subject: Omit<Subject, 'id' | 'created_at' | 'updated_at'>) {
    return this.post<Subject>(`${this.subjectsEndpoint}/`, subject);
  }

  async updateSubject(id: number, subject: Partial<Subject>) {
    return this.put<Subject>(`${this.subjectsEndpoint}/${id}/`, subject);
  }

  async deleteSubject(id: number) {
    return this.delete(`${this.subjectsEndpoint}/${id}/`);
  }

  // Classes
  async getClasses(params?: { academic_year?: string; is_active?: boolean }) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    const query = queryParams.toString();
    const url = query ? `${this.classesEndpoint}/?${query}` : `${this.classesEndpoint}/`;
    return this.get<Class[]>(url);
  }

  async getClass(id: number) {
    return this.get<Class>(`${this.classesEndpoint}/${id}/`);
  }

  async createClass(classData: Omit<Class, 'id' | 'created_at' | 'updated_at'>) {
    return this.post<Class>(`${this.classesEndpoint}/`, classData);
  }

  async updateClass(id: number, classData: Partial<Class>) {
    return this.put<Class>(`${this.classesEndpoint}/${id}/`, classData);
  }

  async deleteClass(id: number) {
    return this.delete(`${this.classesEndpoint}/${id}/`);
  }

  // Academic Years
  async getAcademicYears() {
    return this.get<AcademicYear[]>(`${this.academicYearsEndpoint}/`);
  }

  async getCurrentAcademicYear() {
    return this.get<AcademicYear>(`${this.academicYearsEndpoint}/current/`);
  }

  async createAcademicYear(academicYear: Omit<AcademicYear, 'id' | 'created_at' | 'updated_at'>) {
    return this.post<AcademicYear>(`${this.academicYearsEndpoint}/`, academicYear);
  }

  async updateAcademicYear(id: number, academicYear: Partial<AcademicYear>) {
    return this.put<AcademicYear>(`${this.academicYearsEndpoint}/${id}/`, academicYear);
  }

  async setCurrentAcademicYear(id: number) {
    return this.patch<AcademicYear>(`${this.academicYearsEndpoint}/${id}/set-current/`, {});
  }

  // Grades
  async getGrades(params?: { 
    student?: number; 
    subject?: number; 
    class?: number; 
    exam_type?: string;
    exam_date?: string;
  }) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    const query = queryParams.toString();
    const url = query ? `${this.gradesEndpoint}/?${query}` : `${this.gradesEndpoint}/`;
    return this.get<Grade[]>(url);
  }

  async createGrade(grade: Omit<Grade, 'id' | 'created_at' | 'updated_at'>) {
    return this.post<Grade>(`${this.gradesEndpoint}/`, grade);
  }

  async bulkCreateGrades(grades: Omit<Grade, 'id' | 'created_at' | 'updated_at'>[]) {
    return this.post<{ success: number; errors: any[] }>(`${this.gradesEndpoint}/bulk/`, {
      grades
    });
  }

  async updateGrade(id: number, grade: Partial<Grade>) {
    return this.put<Grade>(`${this.gradesEndpoint}/${id}/`, grade);
  }

  // Attendance
  async getAttendance(params?: {
    student?: number;
    class?: number;
    date?: string;
    date_from?: string;
    date_to?: string;
    status?: string;
  }) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    const query = queryParams.toString();
    const url = query ? `${this.attendanceEndpoint}/?${query}` : `${this.attendanceEndpoint}/`;
    return this.get<Attendance[]>(url);
  }

  async markAttendance(attendance: Omit<Attendance, 'id' | 'created_at' | 'updated_at'>) {
    return this.post<Attendance>(`${this.attendanceEndpoint}/`, attendance);
  }

  async bulkMarkAttendance(attendanceList: Omit<Attendance, 'id' | 'created_at' | 'updated_at'>[]) {
    return this.post<{ success: number; errors: any[] }>(`${this.attendanceEndpoint}/bulk/`, {
      attendance: attendanceList
    });
  }

  async updateAttendance(id: number, attendance: Partial<Attendance>) {
    return this.put<Attendance>(`${this.attendanceEndpoint}/${id}/`, attendance);
  }

  async getAttendanceReport(classId: number, startDate: string, endDate: string) {
    return this.get<any>(`${this.attendanceEndpoint}/report/?class=${classId}&start_date=${startDate}&end_date=${endDate}`);
  }
}

export const academicsService = new AcademicsService();