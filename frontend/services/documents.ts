import { BaseApiService } from './base-api';

export interface Document {
  id?: number;
  title: string;
  description?: string;
  file_url: string;
  file_name: string;
  file_size: number;
  file_type: string;
  category: string;
  tags?: string[];
  student?: number;
  uploaded_by?: number;
  is_public: boolean;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface DocumentCategory {
  id?: number;
  name: string;
  description?: string;
  parent_category?: number;
  is_active: boolean;
  document_count?: number;
  created_at?: string;
  updated_at?: string;
}

export interface StudentDocument {
  id?: number;
  student: number;
  document_type: string;
  document_number?: string;
  file_url: string;
  file_name: string;
  expiry_date?: string;
  status: 'Valid' | 'Expired' | 'Pending' | 'Rejected';
  remarks?: string;
  verified_by?: number;
  verified_at?: string;
  created_at?: string;
  updated_at?: string;
}

export interface UploadResponse {
  file_url: string;
  file_name: string;
  file_size: number;
  file_type: string;
}

export class DocumentsService extends BaseApiService {
  private readonly documentsEndpoint = '/documents';
  private readonly categoriesEndpoint = '/documents/categories';
  private readonly studentDocumentsEndpoint = '/documents/student-documents';
  private readonly uploadEndpoint = '/documents/upload';

  // General Documents
  async getDocuments(params?: {
    category?: string;
    student?: number;
    is_public?: boolean;
    search?: string;
    tags?: string[];
    page?: number;
    limit?: number;
  }) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (Array.isArray(value)) {
            value.forEach(item => queryParams.append(key, item.toString()));
          } else {
            queryParams.append(key, value.toString());
          }
        }
      });
    }
    const query = queryParams.toString();
    const url = query ? `${this.documentsEndpoint}/?${query}` : `${this.documentsEndpoint}/`;
    return this.get<{ results: Document[]; count: number; next?: string; previous?: string }>(url);
  }

  async getDocument(id: number) {
    return this.get<Document>(`${this.documentsEndpoint}/${id}/`);
  }

  async createDocument(document: Omit<Document, 'id' | 'created_at' | 'updated_at'>) {
    return this.post<Document>(`${this.documentsEndpoint}/`, document);
  }

  async updateDocument(id: number, document: Partial<Document>) {
    return this.put<Document>(`${this.documentsEndpoint}/${id}/`, document);
  }

  async deleteDocument(id: number) {
    return this.delete(`${this.documentsEndpoint}/${id}/`);
  }

  async downloadDocument(id: number) {
    return this.get<{ download_url: string }>(`${this.documentsEndpoint}/${id}/download/`);
  }

  // Document Categories
  async getCategories() {
    return this.get<DocumentCategory[]>(`${this.categoriesEndpoint}/`);
  }

  async getCategory(id: number) {
    return this.get<DocumentCategory>(`${this.categoriesEndpoint}/${id}/`);
  }

  async createCategory(category: Omit<DocumentCategory, 'id' | 'created_at' | 'updated_at'>) {
    return this.post<DocumentCategory>(`${this.categoriesEndpoint}/`, category);
  }

  async updateCategory(id: number, category: Partial<DocumentCategory>) {
    return this.put<DocumentCategory>(`${this.categoriesEndpoint}/${id}/`, category);
  }

  async deleteCategory(id: number) {
    return this.delete(`${this.categoriesEndpoint}/${id}/`);
  }

  // Student Documents
  async getStudentDocuments(params?: {
    student?: number;
    document_type?: string;
    status?: string;
    expiry_date_from?: string;
    expiry_date_to?: string;
  }) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    const query = queryParams.toString();
    const url = query ? `${this.studentDocumentsEndpoint}/?${query}` : `${this.studentDocumentsEndpoint}/`;
    return this.get<StudentDocument[]>(url);
  }

  async getStudentDocument(id: number) {
    return this.get<StudentDocument>(`${this.studentDocumentsEndpoint}/${id}/`);
  }

  async createStudentDocument(document: Omit<StudentDocument, 'id' | 'created_at' | 'updated_at'>) {
    return this.post<StudentDocument>(`${this.studentDocumentsEndpoint}/`, document);
  }

  async updateStudentDocument(id: number, document: Partial<StudentDocument>) {
    return this.put<StudentDocument>(`${this.studentDocumentsEndpoint}/${id}/`, document);
  }

  async deleteStudentDocument(id: number) {
    return this.delete(`${this.studentDocumentsEndpoint}/${id}/`);
  }

  async verifyStudentDocument(id: number, status: 'Valid' | 'Rejected', remarks?: string) {
    return this.patch<StudentDocument>(`${this.studentDocumentsEndpoint}/${id}/verify/`, {
      status,
      remarks
    });
  }

  async getExpiredDocuments() {
    return this.get<StudentDocument[]>(`${this.studentDocumentsEndpoint}/expired/`);
  }

  async getExpiringDocuments(days: number = 30) {
    return this.get<StudentDocument[]>(`${this.studentDocumentsEndpoint}/expiring/?days=${days}`);
  }

  // File Upload
  async uploadFile(file: File, category?: string): Promise<{ success: boolean; data?: UploadResponse; error?: string }> {
    const formData = new FormData();
    formData.append('file', file);
    if (category) {
      formData.append('category', category);
    }

    try {
      const response = await fetch(`${(this as any).baseUrl}${this.uploadEndpoint}/`, {
        method: 'POST',
        headers: {
          ...((this as any).defaultHeaders),
          // Remove Content-Type to let browser set it with boundary for FormData
        },
        body: formData,
      });

      // Remove Content-Type from headers for FormData
      const headers = { ...(this as any).defaultHeaders };
      delete headers['Content-Type'];

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `Upload failed: ${response.status}`);
      }

      return {
        success: true,
        data,
      };
    } catch (error) {
      console.error('File upload error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown upload error',
      };
    }
  }

  async uploadMultipleFiles(files: File[], category?: string) {
    const uploadPromises = files.map(file => this.uploadFile(file, category));
    return Promise.all(uploadPromises);
  }

  // Bulk Operations
  async bulkDeleteDocuments(documentIds: number[]) {
    return this.post<{ deleted: number; errors: any[] }>(`${this.documentsEndpoint}/bulk-delete/`, {
      document_ids: documentIds
    });
  }

  async bulkUpdateDocuments(updates: { id: number; data: Partial<Document> }[]) {
    return this.post<{ updated: number; errors: any[] }>(`${this.documentsEndpoint}/bulk-update/`, {
      updates
    });
  }

  // Search and Filter
  async searchDocuments(query: string, filters?: {
    category?: string;
    file_type?: string;
    date_from?: string;
    date_to?: string;
  }) {
    const params = { search: query, ...filters };
    return this.getDocuments(params);
  }

  async getDocumentsByTag(tag: string) {
    return this.getDocuments({ tags: [tag] });
  }

  async getRecentDocuments(limit: number = 10) {
    return this.get<Document[]>(`${this.documentsEndpoint}/recent/?limit=${limit}`);
  }

  // Statistics
  async getDocumentStats() {
    return this.get<{
      total_documents: number;
      total_size: number;
      by_category: { category: string; count: number; size: number }[];
      by_type: { file_type: string; count: number }[];
      recent_uploads: number;
    }>(`${this.documentsEndpoint}/stats/`);
  }
}

export const documentsService = new DocumentsService();