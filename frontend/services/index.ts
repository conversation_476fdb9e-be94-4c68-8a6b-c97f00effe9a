// Base API Service
export { BaseApiService } from './base-api';

// Service Classes
export { AuthService, authService } from './auth';
export { StudentsService, studentsService } from './students';
export { AcademicsService, academicsService } from './academics';
export { FeesService, feesService } from './fees';
export { DocumentsService, documentsService } from './documents';
export { ReportsService, reportsService } from './reports';

// Type Exports
export type {
  // Auth
  User,
  LoginCredentials,
  LoginResponse,
  RegisterData,
  PasswordResetRequest,
  PasswordResetConfirm,
  PasswordChange,
  ProfileUpdate,
  Permission,
  Role,
} from './auth';

export type {
  // Students
  Student,
  StudentListParams,
  StudentListResponse,
} from './students';

export type {
  Subject,
  Class,
  AcademicYear,
  Grade,
  Attendance,
} from './academics';

export type {
  FeeType,
  FeeStructure,
  Payment,
  FeeBalance,
  PaymentSummary,
} from './fees';

export type {
  Document,
  DocumentCategory,
  StudentDocument,
  UploadResponse,
} from './documents';

export type {
  ReportTemplate,
  GeneratedReport,
  StudentReport,
  ClassReport,
  AttendanceReport,
  FinancialReport,
} from './reports';

// Service Instances (ready to use)
export const services = {
  auth: authService,
  students: studentsService,
  academics: academicsService,
  fees: feesService,
  documents: documentsService,
  reports: reportsService,
} as const;

// Utility function to configure all services with auth token
export const setAuthToken = (token: string) => {
  authService.setAuthToken(token);
  studentsService.setAuthToken(token);
  academicsService.setAuthToken(token);
  feesService.setAuthToken(token);
  documentsService.setAuthToken(token);
  reportsService.setAuthToken(token);
};

// Utility function to remove auth token from all services
export const removeAuthToken = () => {
  authService.removeAuthToken();
  studentsService.removeAuthToken();
  academicsService.removeAuthToken();
  feesService.removeAuthToken();
  documentsService.removeAuthToken();
  reportsService.removeAuthToken();
};