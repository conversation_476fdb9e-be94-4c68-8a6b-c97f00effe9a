// Token manager to handle setting auth tokens across all services
// This avoids circular dependencies

import { globalAuthStore } from './global-auth';

export const setAuthTokenForAllServices = async (token: string) => {
  try {
    // Set token in global store - all services will pick it up from there
    globalAuthStore.setToken(token);
    
    // Dynamically import and set token for services if needed
    const [
      { authService },
      { studentsService },
      { academicsService },
      { feesService },
      { documentsService },
      { reportsService }
    ] = await Promise.all([
      import('./auth'),
      import('./students'),
      import('./academics'),
      import('./fees'),
      import('./documents'),
      import('./reports')
    ]);
    
    // Set token for all services
    authService.setAuthToken(token);
    studentsService.setAuthToken(token);
    academicsService.setAuthToken(token);
    feesService.setAuthToken(token);
    documentsService.setAuthToken(token);
    reportsService.setAuthToken(token);
  } catch (error) {
    console.error('Failed to set auth token for services:', error);
  }
};

export const removeAuthTokenFromAllServices = async () => {
  try {
    // Remove token from global store
    globalAuthStore.removeToken();
    
    // Dynamically import and remove token from services
    const [
      { authService },
      { studentsService },
      { academicsService },
      { feesService },
      { documentsService },
      { reportsService }
    ] = await Promise.all([
      import('./auth'),
      import('./students'),
      import('./academics'),
      import('./fees'),
      import('./documents'),
      import('./reports')
    ]);
    
    // Remove token from all services
    authService.removeAuthToken();
    studentsService.removeAuthToken();
    academicsService.removeAuthToken();
    feesService.removeAuthToken();
    documentsService.removeAuthToken();
    reportsService.removeAuthToken();
  } catch (error) {
    console.error('Failed to remove auth token from services:', error);
  }
};