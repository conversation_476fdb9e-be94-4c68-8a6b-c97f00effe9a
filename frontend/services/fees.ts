import { BaseApiService } from './base-api';

export interface FeeType {
  id?: number;
  name: string;
  description?: string;
  amount: number;
  frequency: 'Monthly' | 'Quarterly' | 'Annually' | 'One-time';
  is_mandatory: boolean;
  class_level?: string;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface FeeStructure {
  id?: number;
  academic_year: string;
  class_level: string;
  fee_types: number[];
  total_amount: number;
  discount_percentage?: number;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface Payment {
  id?: number;
  student: number;
  fee_type: number;
  amount_paid: number;
  payment_date: string;
  payment_method: 'Cash' | 'Bank Transfer' | 'Cheque' | 'Card' | 'UPI';
  transaction_id?: string;
  receipt_number: string;
  remarks?: string;
  status: 'Completed' | 'Pending' | 'Failed' | 'Refunded';
  created_by?: number;
  created_at?: string;
  updated_at?: string;
}

export interface FeeBalance {
  id?: number;
  student: number;
  fee_type: number;
  total_amount: number;
  paid_amount: number;
  balance_amount: number;
  due_date?: string;
  status: 'Paid' | 'Partial' | 'Overdue' | 'Pending';
  created_at?: string;
  updated_at?: string;
}

export interface PaymentSummary {
  student_id: number;
  student_name: string;
  total_fees: number;
  total_paid: number;
  balance: number;
  status: string;
  last_payment_date?: string;
}

export class FeesService extends BaseApiService {
  private readonly feeTypesEndpoint = '/fees/types';
  private readonly feeStructuresEndpoint = '/fees/structures';
  private readonly paymentsEndpoint = '/fees/payments';
  private readonly balancesEndpoint = '/fees/balances';

  // Fee Types
  async getFeeTypes(params?: { class_level?: string; is_active?: boolean }) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    const query = queryParams.toString();
    const url = query ? `${this.feeTypesEndpoint}/?${query}` : `${this.feeTypesEndpoint}/`;
    return this.get<FeeType[]>(url);
  }

  async getFeeType(id: number) {
    return this.get<FeeType>(`${this.feeTypesEndpoint}/${id}/`);
  }

  async createFeeType(feeType: Omit<FeeType, 'id' | 'created_at' | 'updated_at'>) {
    return this.post<FeeType>(`${this.feeTypesEndpoint}/`, feeType);
  }

  async updateFeeType(id: number, feeType: Partial<FeeType>) {
    return this.put<FeeType>(`${this.feeTypesEndpoint}/${id}/`, feeType);
  }

  async deleteFeeType(id: number) {
    return this.delete(`${this.feeTypesEndpoint}/${id}/`);
  }

  // Fee Structures
  async getFeeStructures(params?: { academic_year?: string; class_level?: string }) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    const query = queryParams.toString();
    const url = query ? `${this.feeStructuresEndpoint}/?${query}` : `${this.feeStructuresEndpoint}/`;
    return this.get<FeeStructure[]>(url);
  }

  async getFeeStructure(id: number) {
    return this.get<FeeStructure>(`${this.feeStructuresEndpoint}/${id}/`);
  }

  async createFeeStructure(feeStructure: Omit<FeeStructure, 'id' | 'created_at' | 'updated_at'>) {
    return this.post<FeeStructure>(`${this.feeStructuresEndpoint}/`, feeStructure);
  }

  async updateFeeStructure(id: number, feeStructure: Partial<FeeStructure>) {
    return this.put<FeeStructure>(`${this.feeStructuresEndpoint}/${id}/`, feeStructure);
  }

  async deleteFeeStructure(id: number) {
    return this.delete(`${this.feeStructuresEndpoint}/${id}/`);
  }

  // Payments
  async getPayments(params?: {
    student?: number;
    fee_type?: number;
    payment_date?: string;
    payment_method?: string;
    status?: string;
    date_from?: string;
    date_to?: string;
  }) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    const query = queryParams.toString();
    const url = query ? `${this.paymentsEndpoint}/?${query}` : `${this.paymentsEndpoint}/`;
    return this.get<Payment[]>(url);
  }

  async getPayment(id: number) {
    return this.get<Payment>(`${this.paymentsEndpoint}/${id}/`);
  }

  async createPayment(payment: Omit<Payment, 'id' | 'created_at' | 'updated_at'>) {
    return this.post<Payment>(`${this.paymentsEndpoint}/`, payment);
  }

  async updatePayment(id: number, payment: Partial<Payment>) {
    return this.put<Payment>(`${this.paymentsEndpoint}/${id}/`, payment);
  }

  async deletePayment(id: number) {
    return this.delete(`${this.paymentsEndpoint}/${id}/`);
  }

  async generateReceipt(paymentId: number) {
    return this.get<{ receipt_url: string }>(`${this.paymentsEndpoint}/${paymentId}/receipt/`);
  }

  async bulkImportPayments(paymentsData: Omit<Payment, 'id' | 'created_at' | 'updated_at'>[]) {
    return this.post<{ success: number; errors: any[] }>(`${this.paymentsEndpoint}/bulk-import/`, {
      payments: paymentsData
    });
  }

  // Fee Balances
  async getFeeBalances(params?: {
    student?: number;
    fee_type?: number;
    status?: string;
    due_date_from?: string;
    due_date_to?: string;
  }) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    const query = queryParams.toString();
    const url = query ? `${this.balancesEndpoint}/?${query}` : `${this.balancesEndpoint}/`;
    return this.get<FeeBalance[]>(url);
  }

  async getStudentFeeBalance(studentId: number) {
    return this.get<FeeBalance[]>(`${this.balancesEndpoint}/?student=${studentId}`);
  }

  async updateFeeBalance(id: number, balance: Partial<FeeBalance>) {
    return this.put<FeeBalance>(`${this.balancesEndpoint}/${id}/`, balance);
  }

  // Reports and Analytics
  async getPaymentSummary(params?: {
    class_level?: string;
    academic_year?: string;
    date_from?: string;
    date_to?: string;
  }) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    const query = queryParams.toString();
    const url = query ? `/fees/reports/summary/?${query}` : '/fees/reports/summary/';
    return this.get<PaymentSummary[]>(url);
  }

  async getOverduePayments() {
    return this.get<FeeBalance[]>('/fees/reports/overdue/');
  }

  async getDailyCollection(date: string) {
    return this.get<{ total_amount: number; payment_count: number; payments: Payment[] }>(
      `/fees/reports/daily-collection/?date=${date}`
    );
  }

  async getMonthlyCollection(year: number, month: number) {
    return this.get<{ total_amount: number; payment_count: number; daily_breakdown: any[] }>(
      `/fees/reports/monthly-collection/?year=${year}&month=${month}`
    );
  }

  async exportPaymentReport(params?: {
    date_from?: string;
    date_to?: string;
    class_level?: string;
    format?: 'xlsx' | 'csv' | 'pdf';
  }) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    const query = queryParams.toString();
    const url = query ? `/fees/reports/export/?${query}` : '/fees/reports/export/';
    return this.get<{ download_url: string }>(url);
  }
}

export const feesService = new FeesService();