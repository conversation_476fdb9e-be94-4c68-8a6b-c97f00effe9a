// Global authentication token store
// This is shared by all service instances to ensure consistent auth state

class GlobalAuthStore {
  private static instance: GlobalAuthStore;
  private token: string | null = null;

  private constructor() {}

  public static getInstance(): GlobalAuthStore {
    if (!GlobalAuthStore.instance) {
      GlobalAuthStore.instance = new GlobalAuthStore();
    }
    return GlobalAuthStore.instance;
  }

  public setToken(token: string): void {
    this.token = token;
  }

  public getToken(): string | null {
    return this.token;
  }

  public removeToken(): void {
    this.token = null;
  }

  public getAuthHeader(): Record<string, string> {
    if (this.token) {
      return { 'Authorization': `Bearer ${this.token}` };
    }
    return {};
  }
}

export const globalAuthStore = GlobalAuthStore.getInstance();