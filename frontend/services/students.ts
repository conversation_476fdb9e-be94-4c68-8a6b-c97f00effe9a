import { BaseApiService } from './base-api';

export interface Guardian {
  id?: number;
  guardian_type: 'Primary' | 'Secondary';
  first_name: string;
  last_name: string;
  relationship: string;
  cnic?: string;
  phone_number?: string;
  alternate_phone?: string;
  email?: string;
  address_line_1?: string;
  address_line_2?: string;
  city?: string;
  province?: string;
  occupation?: string;
  workplace?: string;
  monthly_income?: number;
  preferred_contact_method?: string;
  can_pickup_student: boolean;
  is_emergency_contact: boolean;
  is_active: boolean;
}

export interface StudentNote {
  id?: number;
  note_type: 'General' | 'Medical' | 'Academic' | 'Disciplinary';
  title: string;
  content: string;
  is_confidential: boolean;
  created_by?: number;
  created_by_name?: string;
  created_at?: string;
  updated_at?: string;
}

export interface Student {
  id?: number;
  student_id?: string;
  first_name: string;
  last_name: string;
  full_name?: string;
  display_name?: string;
  father_name: string;
  date_of_birth: string;
  age?: number;
  gender: 'M' | 'F' | 'O';
  cnic?: string;
  b_form_number?: string;
  phone_number?: string;
  email?: string;
  address_line_1?: string;
  address_line_2?: string;
  city?: string;
  province?: string;
  postal_code?: string;
  religion?: string;
  nationality: string;
  marital_status?: string;
  blood_group?: string;
  photo?: string;
  medical_conditions?: string;
  allergies?: string;
  emergency_medical_contact?: string;
  previous_education?: string;
  matric_marks?: number;
  intermediate_marks?: number;
  is_active: boolean;
  admission_date: string;
  created_at?: string;
  updated_at?: string;
  created_by?: number;
  created_by_name?: string;
  guardians?: Guardian[];
  notes?: StudentNote[];
  primary_guardian?: {
    name: string;
    phone: string;
    relationship: string;
  };
}

export interface StudentListParams {
  page?: number;
  limit?: number;
  search?: string;
  class_level?: string;
  section?: string;
  is_active?: boolean;
}

export interface StudentListResponse {
  results: Student[];
  count: number;
  next?: string;
  previous?: string;
}

export class StudentsService extends BaseApiService {
  private readonly endpoint = '/students';

  async getStudents(params?: StudentListParams) {
    const queryParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }

    const query = queryParams.toString();
    const url = query ? `${this.endpoint}/?${query}` : `${this.endpoint}/`;
    
    return this.get<StudentListResponse>(url);
  }

  async getStudent(id: number) {
    return this.get<Student>(`${this.endpoint}/${id}/`);
  }

  async createStudent(student: Omit<Student, 'id' | 'created_at' | 'updated_at'>) {
    return this.post<Student>(`${this.endpoint}/`, student);
  }

  async updateStudent(id: number, student: Partial<Student>) {
    return this.put<Student>(`${this.endpoint}/${id}/`, student);
  }

  async patchStudent(id: number, student: Partial<Student>) {
    return this.patch<Student>(`${this.endpoint}/${id}/`, student);
  }

  async deleteStudent(id: number) {
    return this.delete(`${this.endpoint}/${id}/`);
  }

  async bulkImportStudents(studentsData: Omit<Student, 'id' | 'created_at' | 'updated_at'>[]) {
    return this.post<{ success: number; errors: any[] }>(`${this.endpoint}/bulk-import/`, {
      students: studentsData
    });
  }

  async getStudentsByClass(classLevel: string, section?: string) {
    const params: StudentListParams = { class_level: classLevel };
    if (section) params.section = section;
    return this.getStudents(params);
  }

  async searchStudents(query: string) {
    return this.getStudents({ search: query });
  }

  async activateStudent(id: number) {
    return this.patch<Student>(`${this.endpoint}/${id}/`, { is_active: true });
  }

  async deactivateStudent(id: number) {
    return this.patch<Student>(`${this.endpoint}/${id}/`, { is_active: false });
  }

  async getStudentStats() {
    return this.get<{
      total_students: number;
      active_students: number;
      inactive_students: number;
      new_this_month: number;
      gender_distribution: {
        male: number;
        female: number;
        other: number;
      };
      province_distribution: Array<{
        province: string;
        count: number;
      }>;
    }>(`${this.endpoint}/stats/`);
  }

  async searchStudentsAdvanced(query: string) {
    return this.get<Student[]>(`${this.endpoint}/search/?q=${encodeURIComponent(query)}`);
  }

  async bulkOperations(operation: 'activate' | 'deactivate' | 'delete', studentIds: number[]) {
    return this.post<{ affected_count: number }>(`${this.endpoint}/bulk/`, {
      operation,
      student_ids: studentIds
    });
  }
}

export const studentsService = new StudentsService();