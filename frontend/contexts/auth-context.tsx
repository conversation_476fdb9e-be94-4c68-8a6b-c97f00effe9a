'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { authService } from '../services/auth';
import { globalAuthStore } from '../services/global-auth';
import { setAuthTokenForAllServices, removeAuthTokenFromAllServices } from '../services/token-manager';
import type { User, LoginCredentials } from '../services/auth';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (credentials: LoginCredentials) => Promise<boolean>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user && authService.isAuthenticated();

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // Check if user is already logged in
        const currentUser = authService.getCurrentUser();
        const token = authService.getAccessToken();

        if (currentUser && token) {
          // Verify token is still valid
          const verifyResponse = await authService.verifyToken();
          if (verifyResponse.status === 'success') {
            setUser(currentUser);
            // Set auth token in global store
            globalAuthStore.setToken(token);
          } else {
            // Token is invalid, try to refresh
            try {
              await authService.refreshAccessToken();
              const refreshedUser = authService.getCurrentUser();
              setUser(refreshedUser);
              // Set new auth token in global store
              globalAuthStore.setToken(authService.getAccessToken() || '');
            } catch (error) {
              // Refresh failed, clear stored data
              authService.logout();
              globalAuthStore.removeToken();
            }
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        authService.logout();
        await removeAuthTokenFromAllServices();
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (credentials: LoginCredentials): Promise<boolean> => {
    try {
      setIsLoading(true);
      const response = await authService.login(credentials);
      
      if (response.status === 'success' && response.data) {
        setUser(response.data.user);
        // Set auth token for all services
        await setAuthTokenForAllServices(authService.getAccessToken() || '');
        return true;
      } else {
        console.error('Login failed:', response.message);
        return false;
      }
    } catch (error) {
      console.error('Login error:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    try {
      setIsLoading(true);
      await authService.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setUser(null);
      await removeAuthTokenFromAllServices();
      setIsLoading(false);
    }
  };

  const refreshToken = async (): Promise<boolean> => {
    try {
      await authService.refreshAccessToken();
      const refreshedUser = authService.getCurrentUser();
      setUser(refreshedUser);
      // Set new auth token for all services
      await setAuthTokenForAllServices(authService.getAccessToken() || '');
      return true;
    } catch (error) {
      console.error('Token refresh failed:', error);
      await logout();
      return false;
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    login,
    logout,
    refreshToken,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}