"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Calendar, Plus, Settings, Users, BookOpen, GraduationCap, Clock } from "lucide-react"
import { AcademicManagerPage } from "@/components/auth"

function SessionsPageContent() {
  const [activeTab, setActiveTab] = useState("overview")

  const stats = [
    {
      title: "Active Academic Year",
      value: "2024-2025",
      subtitle: "Aug 2024 - Jul 2025",
      icon: Calendar,
      color: "bg-blue-500",
    },
    {
      title: "Current Term",
      value: "Fall 2024",
      subtitle: "Semester system",
      icon: BookOpen,
      color: "bg-green-500",
    },
    {
      title: "Total Classes",
      value: "24",
      subtitle: "Across all grades",
      icon: Users,
      color: "bg-indigo-500",
    },
    {
      title: "Enrolled Students",
      value: "2,847",
      subtitle: "Current year",
      icon: GraduationCap,
      color: "bg-purple-500",
    },
  ]

  const academicYears = [
    { year: "2024-2025", status: "Active", startDate: "2024-08-01", endDate: "2025-07-31", students: 2847 },
    { year: "2023-2024", status: "Completed", startDate: "2023-08-01", endDate: "2024-07-31", students: 2654 },
    { year: "2022-2023", status: "Archived", startDate: "2022-08-01", endDate: "2023-07-31", students: 2456 },
  ]

  const currentTerms = [
    { name: "Fall 2024", type: "Semester", startDate: "2024-08-15", endDate: "2024-12-20", status: "Active" },
    { name: "Spring 2025", type: "Semester", startDate: "2025-01-15", endDate: "2025-05-30", status: "Upcoming" },
    { name: "Summer 2025", type: "Summer Session", startDate: "2025-06-01", endDate: "2025-07-31", status: "Planned" },
  ]

  const classes = [
    { name: "Grade 9-A", students: 35, teacher: "Ms. Sarah Ahmed", room: "Room 101" },
    { name: "Grade 9-B", students: 32, teacher: "Mr. Ali Hassan", room: "Room 102" },
    { name: "Grade 10-A", students: 38, teacher: "Ms. Fatima Khan", room: "Room 201" },
    { name: "Grade 10-B", students: 34, teacher: "Mr. Omar Ali", room: "Room 202" },
    { name: "Grade 11-A", students: 29, teacher: "Dr. Ahmed Shah", room: "Room 301" },
    { name: "Grade 12-A", students: 31, teacher: "Ms. Zara Ahmed", room: "Room 401" },
  ]

  return (
    <div className="p-6 space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Academic Sessions</h1>
          <p className="text-gray-600 mt-1">Manage academic years, terms, and class assignments</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            size="sm"
            className="border-indigo-200 text-indigo-700 hover:bg-indigo-50 bg-transparent"
          >
            <Settings className="h-4 w-4 mr-2" />
            Session Settings
          </Button>
          <Button size="sm" className="bg-indigo-600 hover:bg-indigo-700">
            <Plus className="h-4 w-4 mr-2" />
            New Session
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <Card key={index} className="border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <p className="text-3xl font-bold text-gray-900 mt-2">{stat.value}</p>
                  <p className="text-sm text-gray-500 mt-1">{stat.subtitle}</p>
                </div>
                <div className={`p-3 rounded-full ${stat.color}`}>
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <TabsList className="grid w-full grid-cols-4 bg-indigo-50">
            <TabsTrigger value="overview" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white">
              Overview
            </TabsTrigger>
            <TabsTrigger
              value="academic-years"
              className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white"
            >
              Academic Years
            </TabsTrigger>
            <TabsTrigger value="terms" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white">
              Terms/Semesters
            </TabsTrigger>
            <TabsTrigger value="classes" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white">
              Classes
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="border-0 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-indigo-50 to-blue-50 border-b">
                <CardTitle className="text-xl text-indigo-900">Current Session Status</CardTitle>
                <CardDescription>Active academic year and term information</CardDescription>
              </CardHeader>
              <CardContent className="p-6 space-y-4">
                <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-500 rounded-full">
                      <Calendar className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-blue-900">Academic Year 2024-2025</h3>
                      <p className="text-sm text-blue-700">August 1, 2024 - July 31, 2025</p>
                    </div>
                  </div>
                  <Badge className="bg-blue-100 text-blue-800">Active</Badge>
                </div>
                <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg border border-green-200">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-green-500 rounded-full">
                      <BookOpen className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-green-900">Fall 2024 Semester</h3>
                      <p className="text-sm text-green-700">August 15, 2024 - December 20, 2024</p>
                    </div>
                  </div>
                  <Badge className="bg-green-100 text-green-800">Current</Badge>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-purple-50 to-indigo-50 border-b">
                <CardTitle className="text-xl text-purple-900">Upcoming Sessions</CardTitle>
                <CardDescription>Planned academic sessions and terms</CardDescription>
              </CardHeader>
              <CardContent className="p-6 space-y-4">
                <div className="flex items-center justify-between p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-yellow-500 rounded-full">
                      <Clock className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-yellow-900">Spring 2025 Semester</h3>
                      <p className="text-sm text-yellow-700">January 15, 2025 - May 30, 2025</p>
                    </div>
                  </div>
                  <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                    Upcoming
                  </Badge>
                </div>
                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-gray-500 rounded-full">
                      <Calendar className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">Summer 2025 Session</h3>
                      <p className="text-sm text-gray-700">June 1, 2025 - July 31, 2025</p>
                    </div>
                  </div>
                  <Badge variant="outline" className="border-gray-300 text-gray-700">
                    Planned
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="academic-years" className="space-y-6">
          <Card className="border-0 shadow-lg">
            <CardHeader className="bg-gradient-to-r from-indigo-50 to-blue-50 border-b">
              <CardTitle className="text-xl text-indigo-900">Academic Years Management</CardTitle>
              <CardDescription>Configure and manage academic years and their duration</CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-50">
                    <TableHead className="font-semibold text-gray-700">Academic Year</TableHead>
                    <TableHead className="font-semibold text-gray-700">Start Date</TableHead>
                    <TableHead className="font-semibold text-gray-700">End Date</TableHead>
                    <TableHead className="font-semibold text-gray-700">Students</TableHead>
                    <TableHead className="font-semibold text-gray-700">Status</TableHead>
                    <TableHead className="font-semibold text-gray-700">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {academicYears.map((year, index) => (
                    <TableRow key={index} className="hover:bg-gray-50 transition-colors">
                      <TableCell className="font-medium">{year.year}</TableCell>
                      <TableCell>{year.startDate}</TableCell>
                      <TableCell>{year.endDate}</TableCell>
                      <TableCell className="font-semibold text-indigo-600">{year.students.toLocaleString()}</TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            year.status === "Active" ? "default" : year.status === "Completed" ? "secondary" : "outline"
                          }
                          className={
                            year.status === "Active"
                              ? "bg-green-100 text-green-800"
                              : year.status === "Completed"
                                ? "bg-blue-100 text-blue-800"
                                : "bg-gray-100 text-gray-800"
                          }
                        >
                          {year.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button variant="ghost" size="sm" className="text-blue-600 hover:bg-blue-50">
                            Edit
                          </Button>
                          <Button variant="ghost" size="sm" className="text-indigo-600 hover:bg-indigo-50">
                            View Details
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="terms" className="space-y-6">
          <Card className="border-0 shadow-lg">
            <CardHeader className="bg-gradient-to-r from-indigo-50 to-blue-50 border-b">
              <CardTitle className="text-xl text-indigo-900">Terms & Semesters</CardTitle>
              <CardDescription>Manage academic terms and semester configurations</CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="mb-6 p-6 bg-indigo-50 rounded-lg border border-indigo-200">
                <h3 className="text-lg font-semibold text-indigo-900 mb-4">Add New Term</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="termName" className="text-sm font-semibold text-gray-700">
                      Term Name
                    </Label>
                    <Input
                      id="termName"
                      placeholder="e.g., Fall 2024"
                      className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="termType" className="text-sm font-semibold text-gray-700">
                      Term Type
                    </Label>
                    <Select>
                      <SelectTrigger className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500">
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="semester">Semester</SelectItem>
                        <SelectItem value="trimester">Trimester</SelectItem>
                        <SelectItem value="quarter">Quarter</SelectItem>
                        <SelectItem value="summer">Summer Session</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-end">
                    <Button className="w-full bg-indigo-600 hover:bg-indigo-700">Add Term</Button>
                  </div>
                </div>
              </div>

              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-50">
                    <TableHead className="font-semibold text-gray-700">Term Name</TableHead>
                    <TableHead className="font-semibold text-gray-700">Type</TableHead>
                    <TableHead className="font-semibold text-gray-700">Start Date</TableHead>
                    <TableHead className="font-semibold text-gray-700">End Date</TableHead>
                    <TableHead className="font-semibold text-gray-700">Status</TableHead>
                    <TableHead className="font-semibold text-gray-700">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {currentTerms.map((term, index) => (
                    <TableRow key={index} className="hover:bg-gray-50 transition-colors">
                      <TableCell className="font-medium">{term.name}</TableCell>
                      <TableCell>
                        <Badge variant="outline" className="border-purple-200 text-purple-700">
                          {term.type}
                        </Badge>
                      </TableCell>
                      <TableCell>{term.startDate}</TableCell>
                      <TableCell>{term.endDate}</TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            term.status === "Active" ? "default" : term.status === "Upcoming" ? "secondary" : "outline"
                          }
                          className={
                            term.status === "Active"
                              ? "bg-green-100 text-green-800"
                              : term.status === "Upcoming"
                                ? "bg-blue-100 text-blue-800"
                                : "bg-gray-100 text-gray-800"
                          }
                        >
                          {term.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button variant="ghost" size="sm" className="text-blue-600 hover:bg-blue-50">
                            Edit
                          </Button>
                          <Button variant="ghost" size="sm" className="text-red-600 hover:bg-red-50">
                            Delete
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="classes" className="space-y-6">
          <Card className="border-0 shadow-lg">
            <CardHeader className="bg-gradient-to-r from-indigo-50 to-blue-50 border-b">
              <CardTitle className="text-xl text-indigo-900">Class Management</CardTitle>
              <CardDescription>Manage classes and student assignments</CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="mb-6 p-6 bg-indigo-50 rounded-lg border border-indigo-200">
                <h3 className="text-lg font-semibold text-indigo-900 mb-4">Add New Class</h3>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="className" className="text-sm font-semibold text-gray-700">
                      Class Name
                    </Label>
                    <Input
                      id="className"
                      placeholder="e.g., Grade 9-A"
                      className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="classTeacher" className="text-sm font-semibold text-gray-700">
                      Class Teacher
                    </Label>
                    <Input
                      id="classTeacher"
                      placeholder="Teacher name"
                      className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="classRoom" className="text-sm font-semibold text-gray-700">
                      Room
                    </Label>
                    <Input
                      id="classRoom"
                      placeholder="Room number"
                      className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                    />
                  </div>
                  <div className="flex items-end">
                    <Button className="w-full bg-indigo-600 hover:bg-indigo-700">Add Class</Button>
                  </div>
                </div>
              </div>

              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-50">
                    <TableHead className="font-semibold text-gray-700">Class Name</TableHead>
                    <TableHead className="font-semibold text-gray-700">Students</TableHead>
                    <TableHead className="font-semibold text-gray-700">Class Teacher</TableHead>
                    <TableHead className="font-semibold text-gray-700">Room</TableHead>
                    <TableHead className="font-semibold text-gray-700">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {classes.map((classItem, index) => (
                    <TableRow key={index} className="hover:bg-gray-50 transition-colors">
                      <TableCell className="font-medium">{classItem.name}</TableCell>
                      <TableCell>
                        <Badge variant="default" className="bg-indigo-100 text-indigo-800">
                          {classItem.students} students
                        </Badge>
                      </TableCell>
                      <TableCell>{classItem.teacher}</TableCell>
                      <TableCell>
                        <Badge variant="outline" className="border-gray-300 text-gray-700">
                          {classItem.room}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button variant="ghost" size="sm" className="text-indigo-600 hover:bg-indigo-50">
                            View Students
                          </Button>
                          <Button variant="ghost" size="sm" className="text-blue-600 hover:bg-blue-50">
                            Edit
                          </Button>
                          <Button variant="ghost" size="sm" className="text-red-600 hover:bg-red-50">
                            Delete
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default function SessionsPage() {
  return (
    <AcademicManagerPage>
      <SessionsPageContent />
    </AcademicManagerPage>
  )
}
