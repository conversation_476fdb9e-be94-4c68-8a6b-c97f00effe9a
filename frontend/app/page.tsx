import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  Users,
  GraduationCap,
  DollarSign,
  FileText,
  TrendingUp,
  Calendar,
  AlertCircle,
  Clock,
  ArrowUpRight,
  ArrowDownRight,
} from "lucide-react"

export default function Dashboard() {
  const stats = [
    {
      title: "Total Students",
      value: "2,847",
      change: "+12%",
      trend: "up",
      icon: Users,
      color: "bg-blue-500",
    },
    {
      title: "Active Sessions",
      value: "3",
      change: "Current",
      trend: "neutral",
      icon: Calendar,
      color: "bg-green-500",
    },
    {
      title: "Monthly Revenue",
      value: "$245,680",
      change: "+8.2%",
      trend: "up",
      icon: DollarSign,
      color: "bg-indigo-500",
    },
    {
      title: "Pending Fees",
      value: "$45,230",
      change: "-15%",
      trend: "down",
      icon: <PERSON>ert<PERSON>ircle,
      color: "bg-orange-500",
    },
  ]

  const recentActivities = [
    {
      type: "enrollment",
      title: "New Student Enrolled",
      description: "<PERSON> joined Grade 10-A",
      time: "2 hours ago",
      icon: Users,
      color: "text-blue-600 bg-blue-100",
    },
    {
      type: "payment",
      title: "Fee Payment Received",
      description: "$15,000 from Fatima Ali",
      time: "4 hours ago",
      icon: DollarSign,
      color: "text-green-600 bg-green-100",
    },
    {
      type: "document",
      title: "Document Uploaded",
      description: "Medical certificate for Omar Khan",
      time: "6 hours ago",
      icon: FileText,
      color: "text-purple-600 bg-purple-100",
    },
    {
      type: "alert",
      title: "Fee Reminder Sent",
      description: "156 students notified about pending fees",
      time: "1 day ago",
      icon: AlertCircle,
      color: "text-orange-600 bg-orange-100",
    },
  ]

  const upcomingTasks = [
    { task: "Generate monthly fee report", due: "Today", priority: "high" },
    { task: "Review scholarship applications", due: "Tomorrow", priority: "medium" },
    { task: "Update class schedules", due: "Jan 25", priority: "low" },
    { task: "Conduct parent-teacher meetings", due: "Jan 28", priority: "high" },
  ]

  return (
    <div className="p-6 space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-1">Welcome back! Here's what's happening at GIHD today.</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" size="sm">
            <Calendar className="h-4 w-4 mr-2" />
            Academic Year 2024-25
          </Button>
          <Button size="sm" className="bg-indigo-600 hover:bg-indigo-700">
            <TrendingUp className="h-4 w-4 mr-2" />
            View Reports
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <Card key={index} className="relative overflow-hidden border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <p className="text-3xl font-bold text-gray-900 mt-2">{stat.value}</p>
                  <div className="flex items-center mt-2">
                    {stat.trend === "up" && <ArrowUpRight className="h-4 w-4 text-green-500 mr-1" />}
                    {stat.trend === "down" && <ArrowDownRight className="h-4 w-4 text-red-500 mr-1" />}
                    <span
                      className={`text-sm font-medium ${
                        stat.trend === "up"
                          ? "text-green-600"
                          : stat.trend === "down"
                            ? "text-red-600"
                            : "text-gray-600"
                      }`}
                    >
                      {stat.change}
                    </span>
                  </div>
                </div>
                <div className={`p-3 rounded-full ${stat.color}`}>
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent Activities */}
        <Card className="lg:col-span-2 border-0 shadow-lg">
          <CardHeader className="bg-gradient-to-r from-indigo-50 to-blue-50 border-b">
            <CardTitle className="text-xl text-indigo-900">Recent Activities</CardTitle>
            <CardDescription>Latest updates and activities in the system</CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            <div className="divide-y divide-gray-100">
              {recentActivities.map((activity, index) => (
                <div key={index} className="p-6 hover:bg-gray-50 transition-colors">
                  <div className="flex items-start space-x-4">
                    <div className={`p-2 rounded-full ${activity.color}`}>
                      <activity.icon className="h-4 w-4" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900">{activity.title}</p>
                      <p className="text-sm text-gray-600 mt-1">{activity.description}</p>
                      <p className="text-xs text-gray-400 mt-2 flex items-center">
                        <Clock className="h-3 w-3 mr-1" />
                        {activity.time}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Quick Stats & Tasks */}
        <div className="space-y-6">
          {/* Fee Collection Progress */}
          <Card className="border-0 shadow-lg">
            <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 border-b">
              <CardTitle className="text-lg text-green-900">Fee Collection</CardTitle>
              <CardDescription>Current month progress</CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-600">Collected</span>
                  <span className="text-sm font-bold text-green-600">$200,450</span>
                </div>
                <Progress value={82} className="h-2" />
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>82% of target</span>
                  <span>$245,680 goal</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Upcoming Tasks */}
          <Card className="border-0 shadow-lg">
            <CardHeader className="bg-gradient-to-r from-purple-50 to-indigo-50 border-b">
              <CardTitle className="text-lg text-purple-900">Upcoming Tasks</CardTitle>
              <CardDescription>Important tasks and deadlines</CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <div className="divide-y divide-gray-100">
                {upcomingTasks.map((task, index) => (
                  <div key={index} className="p-4 hover:bg-gray-50 transition-colors">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">{task.task}</p>
                        <p className="text-xs text-gray-500 mt-1">Due: {task.due}</p>
                      </div>
                      <Badge
                        variant={
                          task.priority === "high"
                            ? "destructive"
                            : task.priority === "medium"
                              ? "default"
                              : "secondary"
                        }
                        className="text-xs"
                      >
                        {task.priority}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Quick Actions */}
      <Card className="border-0 shadow-lg">
        <CardHeader className="bg-gradient-to-r from-indigo-50 to-blue-50 border-b">
          <CardTitle className="text-xl text-indigo-900">Quick Actions</CardTitle>
          <CardDescription>Frequently used actions and shortcuts</CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Button className="h-20 flex-col space-y-2 bg-indigo-600 hover:bg-indigo-700">
              <Users className="h-6 w-6" />
              <span>Add Student</span>
            </Button>
            <Button
              variant="outline"
              className="h-20 flex-col space-y-2 border-indigo-200 hover:bg-indigo-50 bg-transparent"
            >
              <DollarSign className="h-6 w-6 text-indigo-600" />
              <span>Record Payment</span>
            </Button>
            <Button
              variant="outline"
              className="h-20 flex-col space-y-2 border-indigo-200 hover:bg-indigo-50 bg-transparent"
            >
              <FileText className="h-6 w-6 text-indigo-600" />
              <span>Generate Report</span>
            </Button>
            <Button
              variant="outline"
              className="h-20 flex-col space-y-2 border-indigo-200 hover:bg-indigo-50 bg-transparent"
            >
              <GraduationCap className="h-6 w-6 text-indigo-600" />
              <span>Manage Classes</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
