'use client';

import { useEffect } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { useAuth } from '../contexts/auth-context';

interface AuthGuardProps {
  children: React.ReactNode;
}

// Routes that don't require authentication
const publicRoutes = ['/login', '/register', '/forgot-password'];

// Loading component
const LoadingScreen = () => (
  <div className="min-h-screen flex items-center justify-center bg-gray-50">
    <div className="text-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
      <p className="text-gray-600">Loading...</p>
    </div>
  </div>
);

export function AuthGuard({ children }: AuthGuardProps) {
  const { user, isLoading, isAuthenticated } = useAuth();
  const pathname = usePathname();
  const router = useRouter();

  useEffect(() => {
    if (isLoading) return; // Wait for auth initialization to complete

    const isPublicRoute = publicRoutes.includes(pathname);

    if (!isAuthenticated && !isPublicRoute) {
      // User is not authenticated and trying to access a protected route
      router.push('/login');
      return;
    }

    if (isAuthenticated && pathname === '/login') {
      // User is authenticated but on login page, redirect to dashboard
      router.push('/');
      return;
    }
  }, [isAuthenticated, isLoading, pathname, router]);

  // Show loading screen while auth is initializing
  if (isLoading) {
    return <LoadingScreen />;
  }

  // Show login page for unauthenticated users on public routes
  if (!isAuthenticated && publicRoutes.includes(pathname)) {
    return <>{children}</>;
  }

  // Show content for authenticated users
  if (isAuthenticated) {
    return <>{children}</>;
  }

  // Fallback loading state during redirect
  return <LoadingScreen />;
}