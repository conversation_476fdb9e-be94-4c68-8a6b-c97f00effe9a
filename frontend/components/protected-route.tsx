'use client';

import { useAuth } from '../contexts/auth-context';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermissions?: string[];
  requiredRole?: 'admin' | 'teacher' | 'staff' | 'parent';
  fallbackPath?: string;
}

export function ProtectedRoute({ 
  children, 
  requiredPermissions = [], 
  requiredRole,
  fallbackPath = '/unauthorized'
}: ProtectedRouteProps) {
  const { user, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (isLoading) return;

    if (!isAuthenticated) {
      router.push('/login');
      return;
    }

    if (user) {
      // Check role requirement
      if (requiredRole && user.role !== requiredRole && user.role !== 'admin') {
        router.push(fallbackPath);
        return;
      }

      // Check permissions requirement
      if (requiredPermissions.length > 0) {
        const hasAllPermissions = requiredPermissions.every(permission =>
          user.permissions.includes(permission)
        );

        if (!hasAllPermissions && user.role !== 'admin') {
          router.push(fallbackPath);
          return;
        }
      }
    }
  }, [user, isAuthenticated, isLoading, requiredPermissions, requiredRole, router, fallbackPath]);

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Show nothing while redirecting
  if (!isAuthenticated) {
    return null;
  }

  // Show children if all checks pass
  return <>{children}</>;
}

// Higher-order component for easy page protection
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  options?: {
    requiredPermissions?: string[];
    requiredRole?: 'admin' | 'teacher' | 'staff' | 'parent';
    fallbackPath?: string;
  }
) {
  return function AuthenticatedComponent(props: P) {
    return (
      <ProtectedRoute {...options}>
        <Component {...props} />
      </ProtectedRoute>
    );
  };
}

// Specific wrappers for common use cases
export const AdminOnly = ({ children }: { children: React.ReactNode }) => (
  <ProtectedRoute requiredRole="admin">
    {children}
  </ProtectedRoute>
);

export const TeacherOrAdmin = ({ children }: { children: React.ReactNode }) => (
  <ProtectedRoute requiredPermissions={['academics.view_course']}>
    {children}
  </ProtectedRoute>
);

export const StaffOrAdmin = ({ children }: { children: React.ReactNode }) => (
  <ProtectedRoute requiredPermissions={['students.view_student']}>
    {children}
  </ProtectedRoute>
);