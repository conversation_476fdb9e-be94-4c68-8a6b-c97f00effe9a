'use client';

import React, { ReactNode } from 'react';
import { AuthGuard } from './auth-guard';

interface ProtectedLayoutProps {
  children: ReactNode;
  requireAuth?: boolean;
  requiredRole?: 'admin' | 'teacher' | 'staff' | 'parent';
  requiredPermissions?: string[];
  redirectTo?: string;
}

// Protected layout for admin pages
export function AdminLayout({ children }: { children: ReactNode }) {
  return (
    <AuthGuard 
      requireAuth={true} 
      requiredRole="admin"
      redirectTo="/login"
    >
      {children}
    </AuthGuard>
  );
}

// Protected layout for teacher pages
export function TeacherLayout({ children }: { children: ReactNode }) {
  return (
    <AuthGuard 
      requireAuth={true} 
      requiredRole="teacher"
      redirectTo="/login"
    >
      {children}
    </AuthGuard>
  );
}

// Protected layout for staff pages
export function StaffLayout({ children }: { children: ReactNode }) {
  return (
    <AuthGuard 
      requireAuth={true} 
      requiredRole="staff"
      redirectTo="/login"
    >
      {children}
    </AuthGuard>
  );
}

// Protected layout for parent pages
export function ParentLayout({ children }: { children: ReactNode }) {
  return (
    <AuthGuard 
      requireAuth={true} 
      requiredRole="parent"
      redirectTo="/login"
    >
      {children}
    </AuthGuard>
  );
}

// General protected layout (any authenticated user)
export function ProtectedLayout({ 
  children, 
  requireAuth = true,
  requiredRole,
  requiredPermissions,
  redirectTo = '/login'
}: ProtectedLayoutProps) {
  return (
    <AuthGuard 
      requireAuth={requireAuth}
      requiredRole={requiredRole}
      requiredPermissions={requiredPermissions}
      redirectTo={redirectTo}
    >
      {children}
    </AuthGuard>
  );
}

// Layout for pages that require specific permissions
export function PermissionLayout({ 
  children, 
  permissions 
}: { 
  children: ReactNode; 
  permissions: string[] 
}) {
  return (
    <AuthGuard 
      requireAuth={true}
      requiredPermissions={permissions}
      redirectTo="/login"
    >
      {children}
    </AuthGuard>
  );
}

// Student management specific layout
export function StudentManagementLayout({ children }: { children: ReactNode }) {
  return (
    <PermissionLayout permissions={['students.view_student']}>
      {children}
    </PermissionLayout>
  );
}

// Academic management specific layout
export function AcademicManagementLayout({ children }: { children: ReactNode }) {
  return (
    <PermissionLayout permissions={['academics.view_academicyear']}>
      {children}
    </PermissionLayout>
  );
}

// Fee management specific layout
export function FeeManagementLayout({ children }: { children: ReactNode }) {
  return (
    <PermissionLayout permissions={['fees.view_feestructure']}>
      {children}
    </PermissionLayout>
  );
}

// Document management specific layout
export function DocumentManagementLayout({ children }: { children: ReactNode }) {
  return (
    <PermissionLayout permissions={['documents.view_document']}>
      {children}
    </PermissionLayout>
  );
}

// Report management specific layout
export function ReportManagementLayout({ children }: { children: ReactNode }) {
  return (
    <PermissionLayout permissions={['reports.view_report']}>
      {children}
    </PermissionLayout>
  );
}