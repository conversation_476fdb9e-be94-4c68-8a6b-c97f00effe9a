'use client';

import React, { ReactNode, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/auth-context';
import { Loader2 } from 'lucide-react';

interface AuthGuardProps {
  children: ReactNode;
  requireAuth?: boolean;
  requiredRole?: 'admin' | 'teacher' | 'staff' | 'parent';
  requiredPermissions?: string[];
  redirectTo?: string;
  fallback?: ReactNode;
}

export function AuthGuard({
  children,
  requireAuth = true,
  requiredRole,
  requiredPermissions = [],
  redirectTo = '/login',
  fallback
}: AuthGuardProps) {
  const { isAuthenticated, isLoading, user } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && requireAuth && !isAuthenticated) {
      router.push(redirectTo);
    }
  }, [isAuthenticated, isLoading, requireAuth, redirectTo, router]);

  // Show loading state while checking authentication
  if (isLoading) {
    return fallback || (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
        <span className="ml-2 text-lg text-gray-600">Loading...</span>
      </div>
    );
  }

  // If authentication is required but user is not authenticated
  if (requireAuth && !isAuthenticated) {
    return null; // Router will handle redirect
  }

  // If no authentication is required, show content
  if (!requireAuth) {
    return <>{children}</>;
  }

  // Check role requirements
  if (requiredRole && user?.role !== requiredRole) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="text-6xl text-red-500 mb-4">🚫</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
          <p className="text-gray-600 mb-4">
            You don't have the required role ({requiredRole}) to access this page.
          </p>
          <button
            onClick={() => router.back()}
            className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  // Check permission requirements
  if (requiredPermissions.length > 0) {
    const userPermissions = user?.permissions || [];
    const hasAllPermissions = requiredPermissions.every(permission => 
      userPermissions.includes(permission)
    );

    if (!hasAllPermissions) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="text-6xl text-red-500 mb-4">🚫</div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
            <p className="text-gray-600 mb-4">
              You don't have the required permissions to access this page.
            </p>
            <button
              onClick={() => router.back()}
              className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700"
            >
              Go Back
            </button>
          </div>
        </div>
      );
    }
  }

  // All checks passed, render children
  return <>{children}</>;
}

// Higher Order Component version for easier use
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  options: Omit<AuthGuardProps, 'children'> = {}
) {
  return function AuthenticatedComponent(props: P) {
    return (
      <AuthGuard {...options}>
        <Component {...props} />
      </AuthGuard>
    );
  };
}

// Hook for checking auth status in components
export function useAuthGuard(options: {
  requireAuth?: boolean;
  requiredRole?: string;
  requiredPermissions?: string[];
} = {}) {
  const { isAuthenticated, isLoading, user } = useAuth();
  const {
    requireAuth = true,
    requiredRole,
    requiredPermissions = []
  } = options;

  const hasAccess = React.useMemo(() => {
    if (!requireAuth) return true;
    if (!isAuthenticated) return false;
    
    if (requiredRole && user?.role !== requiredRole) return false;
    
    if (requiredPermissions.length > 0) {
      const userPermissions = user?.permissions || [];
      const hasAllPermissions = requiredPermissions.every(permission => 
        userPermissions.includes(permission)
      );
      if (!hasAllPermissions) return false;
    }

    return true;
  }, [isAuthenticated, user, requireAuth, requiredRole, requiredPermissions]);

  return {
    isAuthenticated,
    isLoading,
    user,
    hasAccess,
    hasRole: (role: string) => user?.role === role,
    hasPermission: (permission: string) => user?.permissions?.includes(permission) || false,
    hasAnyPermission: (permissions: string[]) => 
      permissions.some(permission => user?.permissions?.includes(permission)),
    hasAllPermissions: (permissions: string[]) => 
      permissions.every(permission => user?.permissions?.includes(permission))
  };
}