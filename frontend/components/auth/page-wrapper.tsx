'use client';

import React, { ReactNode } from 'react';
import { AuthGuard } from './auth-guard';

interface PageWrapperProps {
  children: ReactNode;
  auth?: {
    required?: boolean;
    role?: 'admin' | 'teacher' | 'staff' | 'parent';
    permissions?: string[];
    redirectTo?: string;
  };
}

export function PageWrapper({ children, auth }: PageWrapperProps) {
  // If no auth config is provided, assume page requires authentication
  const authConfig = auth || { required: true };

  return (
    <AuthGuard
      requireAuth={authConfig.required}
      requiredRole={authConfig.role}
      requiredPermissions={authConfig.permissions}
      redirectTo={authConfig.redirectTo}
    >
      {children}
    </AuthGuard>
  );
}

// Predefined wrappers for common page types
export const PublicPage = ({ children }: { children: ReactNode }) => (
  <PageWrapper auth={{ required: false }}>
    {children}
  </PageWrapper>
);

export const PrivatePage = ({ children }: { children: ReactNode }) => (
  <PageWrapper auth={{ required: true }}>
    {children}
  </PageWrapper>
);

export const AdminPage = ({ children }: { children: ReactNode }) => (
  <PageWrapper auth={{ required: true, role: 'admin' }}>
    {children}
  </PageWrapper>
);

export const TeacherPage = ({ children }: { children: ReactNode }) => (
  <PageWrapper auth={{ required: true, role: 'teacher' }}>
    {children}
  </PageWrapper>
);

export const StaffPage = ({ children }: { children: ReactNode }) => (
  <PageWrapper auth={{ required: true, role: 'staff' }}>
    {children}
  </PageWrapper>
);

export const ParentPage = ({ children }: { children: ReactNode }) => (
  <PageWrapper auth={{ required: true, role: 'parent' }}>
    {children}
  </PageWrapper>
);

// Permission-based wrappers
export const StudentManagerPage = ({ children }: { children: ReactNode }) => (
  <PageWrapper auth={{ required: true, permissions: ['students.view_student'] }}>
    {children}
  </PageWrapper>
);

export const AcademicManagerPage = ({ children }: { children: ReactNode }) => (
  <PageWrapper auth={{ required: true, permissions: ['academics.view_academicyear'] }}>
    {children}
  </PageWrapper>
);

export const FeeManagerPage = ({ children }: { children: ReactNode }) => (
  <PageWrapper auth={{ required: true, permissions: ['fees.view_feestructure'] }}>
    {children}
  </PageWrapper>
);

export const DocumentManagerPage = ({ children }: { children: ReactNode }) => (
  <PageWrapper auth={{ required: true, permissions: ['documents.view_document'] }}>
    {children}
  </PageWrapper>
);

export const ReportManagerPage = ({ children }: { children: ReactNode }) => (
  <PageWrapper auth={{ required: true, permissions: ['reports.view_report'] }}>
    {children}
  </PageWrapper>
);