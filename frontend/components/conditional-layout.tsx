'use client';

import { usePathname } from 'next/navigation';
import { AdminLayout } from './admin-layout';
import { ReactNode } from 'react';

interface ConditionalLayoutProps {
  children: ReactNode;
}

// Routes that should not use AdminLayout
const noLayoutRoutes = ['/login', '/register', '/forgot-password', '/unauthorized'];

export function ConditionalLayout({ children }: ConditionalLayoutProps) {
  const pathname = usePathname();
  
  // Check if current route should not use AdminLayout
  const shouldUseLayout = !noLayoutRoutes.includes(pathname);
  
  if (shouldUseLayout) {
    return <AdminLayout>{children}</AdminLayout>;
  }
  
  // For login/register pages, render children directly
  return <>{children}</>;
}