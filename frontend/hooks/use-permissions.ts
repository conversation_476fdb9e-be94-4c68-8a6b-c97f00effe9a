'use client';

import { useAuth } from '../contexts/auth-context';

export function usePermissions() {
  const { user } = useAuth();

  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    if (user.role === 'admin') return true; // Admin has all permissions
    return user.permissions.includes(permission);
  };

  const hasRole = (role: string): boolean => {
    if (!user) return false;
    return user.role === role;
  };

  const hasAnyRole = (roles: string[]): boolean => {
    if (!user) return false;
    return roles.includes(user.role);
  };

  const hasAllPermissions = (permissions: string[]): boolean => {
    if (!user) return false;
    if (user.role === 'admin') return true;
    return permissions.every(permission => user.permissions.includes(permission));
  };

  const hasAnyPermission = (permissions: string[]): boolean => {
    if (!user) return false;
    if (user.role === 'admin') return true;
    return permissions.some(permission => user.permissions.includes(permission));
  };

  const isAdmin = (): boolean => hasRole('admin');
  const isTeacher = (): boolean => hasRole('teacher');
  const isStaff = (): boolean => hasRole('staff');
  const isParent = (): boolean => hasRole('parent');

  const canManageStudents = (): boolean => 
    hasAnyPermission(['students.add_student', 'students.change_student', 'students.delete_student']);

  const canViewReports = (): boolean => 
    hasAnyPermission(['reports.view_report', 'academics.view_course', 'fees.view_payment']);

  const canManageFees = (): boolean => 
    hasAnyPermission(['fees.add_payment', 'fees.change_payment', 'fees.delete_payment']);

  const canManageAcademics = (): boolean => 
    hasAnyPermission(['academics.add_course', 'academics.change_course', 'academics.delete_course']);

  return {
    user,
    hasPermission,
    hasRole,
    hasAnyRole,
    hasAllPermissions,
    hasAnyPermission,
    isAdmin,
    isTeacher,
    isStaff,
    isParent,
    canManageStudents,
    canViewReports,
    canManageFees,
    canManageAcademics,
  };
}